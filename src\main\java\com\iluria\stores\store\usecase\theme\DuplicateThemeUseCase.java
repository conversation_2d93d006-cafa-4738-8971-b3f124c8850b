package com.iluria.stores.store.usecase.theme;

import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeGateway;
import com.iluria.exception.StoreErrorMessageEnum;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;

@Service
@RequiredArgsConstructor
public class DuplicateThemeUseCase {

    private final ThemeGateway themeGateway;
    private final ThemeTemplateManagerUseCase themeTemplateManagerUseCase;

    @Transactional
    public Theme execute(ULID themeId, ULID storeId) {
        // Buscar o tema original
        Theme originalTheme = themeGateway.findById(themeId);
        if (originalTheme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }

        // Verificar se o tema pertence à loja
        if (!originalTheme.getStoreId().equals(storeId)) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }

        // Gerar nome único para o tema duplicado
        String duplicatedName = generateUniqueName(originalTheme.getName(), storeId);

        // Criar o tema duplicado
        Theme duplicatedTheme = createDuplicatedTheme(originalTheme, duplicatedName, storeId);

        // Salvar o tema duplicado
        Theme savedTheme = themeGateway.save(duplicatedTheme);

        // Copiar arquivos de template se existirem
        if (originalTheme.getHasTemplateFiles() && originalTheme.getTemplateFiles() != null && !originalTheme.getTemplateFiles().isEmpty()) {
            copyTemplateFiles(originalTheme, savedTheme);
            // Salvar novamente com as informações de template atualizadas
            savedTheme = themeGateway.save(savedTheme);
        }

        return savedTheme;
    }

    private String generateUniqueName(String originalName, ULID storeId) {
        String baseName = originalName + " - Duplicado";
        String finalName = baseName;
        int counter = 1;

        // Verificar se já existe um tema com esse nome e gerar nome único
        while (themeGateway.existsByStoreIdAndName(storeId, finalName)) {
            finalName = baseName + " (" + counter + ")";
            counter++;

            // Limite de segurança para evitar loop infinito
            if (counter > 100) {
                finalName = originalName + " - Cópia " + System.currentTimeMillis();
                break;
            }
        }

        return finalName;
    }

    private Theme createDuplicatedTheme(Theme originalTheme, String duplicatedName, ULID storeId) {
        ULID newThemeId = ULID.random();
        
        return Theme.builder()
                .id(newThemeId)
                .storeId(storeId)
                .name(duplicatedName)
                .description(originalTheme.getDescription() != null ? 
                    originalTheme.getDescription() : "Tema duplicado de " + originalTheme.getName())
                .categoryId(originalTheme.getCategoryId())
                .previewImageUrl(originalTheme.getPreviewImageUrl())
                .author(originalTheme.getAuthor())
                .version(originalTheme.getVersion())
                .isPremium(false) // Temas duplicados não são premium
                .isDefault(false) // Temas duplicados não são padrão
                .isActive(false) // Não ativar automaticamente
                .cssVariables(originalTheme.getCssVariables())
                .typography(originalTheme.getTypography())
                .spacing(originalTheme.getSpacing())
                .customizations(originalTheme.getCustomizations())
                // Configurações de template - gerar path único
                .templateS3Path(originalTheme.getTemplateS3Path() != null ? 
                    "stores/" + storeId + "/themes/" + newThemeId + "/templates/" : null)
                .templateFiles(originalTheme.getTemplateFiles() != null ? 
                    new HashSet<>(originalTheme.getTemplateFiles()) : null)
                .templateVersion(originalTheme.getTemplateVersion())
                .templateChecksum(originalTheme.getTemplateChecksum())
                .hasTemplateFiles(originalTheme.getHasTemplateFiles())
                .templateUpdatedAt(originalTheme.getTemplateUpdatedAt())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    private void copyTemplateFiles(Theme sourceTheme, Theme destTheme) {
        try {
            // Baixar os templates do tema original
            var templateFiles = themeTemplateManagerUseCase.downloadTemplateFiles(sourceTheme.getId());

            if (templateFiles != null && !templateFiles.isEmpty()) {
                // Upload dos templates para o novo path
                themeTemplateManagerUseCase.uploadTemplateFiles(
                    destTheme.getId(),
                    templateFiles,
                    sourceTheme.getTemplateVersion()
                );

            }
        } catch (Exception e) {
            // Se falhar ao copiar templates, limpar configurações do tema de destino
            try {
                destTheme.setHasTemplateFiles(false);
                destTheme.setTemplateFiles(null);
                destTheme.setTemplateS3Path(null);
                destTheme.setTemplateChecksum(null);
                destTheme.setTemplateUpdatedAt(null);

                themeGateway.save(destTheme);
            } catch (Exception cleanupError) {
                // Ignore cleanup errors
            }
        }
    }
}
