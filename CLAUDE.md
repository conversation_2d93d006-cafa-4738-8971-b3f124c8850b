# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Building and Running
- **Build the project**: `mvn clean compile`
- **Run tests**: `mvn test`
- **Run tests with coverage**: `mvn test jacoco:report`
- **Start application**: `mvn spring-boot:run`
- **Package application**: `mvn clean package`

### Database Operations
- **Start development database**: `docker-compose -f iluria-db/compose.yaml up -d`
- **Stop development database**: `docker-compose -f iluria-db/compose.yaml down`
- **Reset database**: Stop containers, remove volumes, then start again

### Test Commands
- **Run specific test class**: `mvn test -Dtest=ClassName`
- **Run tests in specific package**: `mvn test -Dtest="com.iluria.store.usecases.address.*"`
- **Run tests with TestContainers**: Tests automatically use TestContainers for database isolation

## Architecture Overview

This is a Spring Boot 3.4.6 application using **Clean Architecture** principles with the following layers:

### Package Structure
- **`domain/`** - Domain entities and business logic (no framework dependencies)
- **`usecase/`** - Application business rules and orchestration
- **`gateway/`** - Interfaces for external systems (database, APIs, file storage)
- **`entrypoint/`** - External interfaces (REST controllers, DTOs, mappers)
- **`config/`** - Spring configuration classes
- **`exception/`** - Custom exceptions and error handling

### Core Modules
- **`auth/`** - Authentication, JWT, MFA, user management
- **`store/`** - Core store functionality (customers, pages, settings, etc.)
- **`products/`** - Product catalog, categories, attributes, measurements
- **`order/`** - Order processing and management
- **`shipping/`** - Shipping calculations and Correios integration
- **`promotion/`** - Promotions and discounts
- **`layoutEditor/`** - Dynamic layout management
- **`fileManager/`** - File operations and S3 integration

### Key Technologies
- **Spring Boot 3.4.6** with Java 21
- **Spring Security** with JWT authentication
- **Spring Data JPA** with MySQL
- **Flyway** for database migrations
- **MapStruct** for object mapping
- **TestContainers** for integration testing
- **AWS S3** for file storage
- **Correios API** for shipping calculations
- **MercadoPago** for payment processing

## Database Configuration

### Development Environment
- **Local MySQL**: `localhost:3308/storeDB`
- **Docker**: Use `iluria-db/compose.yaml` for local development
- **Flyway locations**: `classpath:db/migration,classpath:db/dev`

### Testing
- **TestContainers**: Automatically provisions MySQL containers for tests
- **H2**: Available for unit tests requiring database access
- **Test configuration**: `src/test/resources/application-test.properties`

## Key Integration Points

### AWS Services
- **S3 buckets**: `iluria-bucket-dev` (general), `iluria-export-bucket-dev` (exports)
- **SES**: Email notifications and transactional emails
- **Load balancer**: Target group configuration for deployment

### External APIs
- **Correios API**: Shipping calculations and delivery time estimates
- **MercadoPago**: Payment processing and OAuth integration
- **CEP API**: Address validation and postal code lookup

## Development Patterns

### Clean Architecture Flow
1. **Controller** receives request and validates DTOs
2. **UseCase** orchestrates business logic
3. **Gateway** interfaces handle external dependencies
4. **Domain** entities contain business rules
5. **Mapper** classes convert between layers

### File Upload Handling
- **S3 integration** for image storage
- **Multipart configuration**: Max 15MB file size
- **Image types**: Product images, category images, store assets

### Exception Handling
- **Global exception handler**: `StoreGlobalExceptionHandler`
- **Custom exceptions**: Domain-specific exceptions in each module
- **Error responses**: Standardized error message format

## Testing Strategy

### Test Categories
- **Unit tests**: Domain logic and use cases
- **Integration tests**: Database operations with TestContainers
- **Controller tests**: REST API endpoints
- **Gateway tests**: External service integrations

### Test Fixtures
- **Fixture classes**: Pre-built test data in `src/test/java/com/iluria/store/fixture/`
- **Test containers**: Automatic MySQL container provisioning
- **Test profiles**: Separate configuration for testing

## Configuration Management

### Environment Profiles
- **Development**: `application-dev.properties`
- **Production**: `application.properties`
- **Testing**: `application-test.properties`

### Key Configuration Areas
- **Database**: MySQL connection and JPA settings
- **Security**: JWT keys, CORS configuration
- **File upload**: Multipart and S3 settings
- **External APIs**: Correios, MercadoPago, AWS credentials
- **Monitoring**: Prometheus metrics exposure

## Security Implementation

### Authentication
- **JWT tokens**: RSA-signed tokens with public/private key pairs
- **MFA support**: TOTP-based multi-factor authentication
- **Password encoding**: BCrypt password hashing

### Authorization
- **Role-based**: User roles and permissions
- **CORS**: Configurable allowed origins
- **Security filter**: Stateless session management

## Development Guidelines

### Logging Policy
- **NEVER use any form of logging** (System.out.println, log.debug, log.info, log.error, etc.)
- **Exception**: Only critical error logs that are absolutely necessary for production monitoring
- **Remove all existing logs** when modifying code

### Testing Policy
- **DO NOT create unit tests** unless explicitly requested by the user
- **Focus on code quality** through proper architecture and error handling
- **Integration tests only** when specifically required for critical paths

## Deployment Notes

### Build Requirements
- **Java 21**: Required runtime version
- **Maven**: Build tool with annotation processing
- **Docker**: For local database development

### Key Dependencies
- **Spring Cloud**: OpenFeign for API clients
- **Lombok**: Code generation for boilerplate
- **MapStruct**: Type-safe mapping between DTOs and entities
- **Apache POI**: Excel file processing for exports
- **OpenCSV**: CSV file processing for data imports/exports