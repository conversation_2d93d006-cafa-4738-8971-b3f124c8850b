package com.iluria.stores.store.entrypoint.controller;

import com.iluria.commons.service.AuthorizationContext;
import com.iluria.commons.domain.FileManager;
import com.iluria.commons.domain.FileManagerEnum;
import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.entrypoint.controller.dto.CreateThemeRequestDTO;
import com.iluria.stores.store.entrypoint.controller.dto.ThemeDTO;
import com.iluria.stores.store.entrypoint.controller.dto.UpdateThemeRequestDTO;
import com.iluria.stores.store.entrypoint.controller.mapper.ThemeDTOMapper;
import com.iluria.stores.store.usecase.theme.*;
import com.iluria.stores.store.usecase.theme.FindDefaultThemesUseCase;
import com.iluria.stores.store.usecase.theme.DuplicateThemeUseCase;
import com.iluria.stores.store.usecase.theme.CleanupOrphanedFilesUseCase;
import com.iluria.stores.store.usecase.theme.InitializeDefaultThemesUseCase;
import com.iluria.stores.store.usecase.theme.FindStoreSpecificThemesUseCase;
import com.iluria.stores.store.usecase.theme.CopyDefaultThemeUseCase;
import com.iluria.stores.store.usecase.theme.SyncThemeTemplatesUseCase;
import com.iluria.stores.store.service.ThemeProgressService;
import com.iluria.stores.store.service.AsyncSitePublishingProgressService;
import com.iluria.stores.store.service.ThemeBackupService;
import com.iluria.stores.store.domain.ThemeBackup;
import com.iluria.commons.usecase.domainUrl.FindDomainUrlUseCase;
import com.iluria.commons.domain.DomainUrl;
import com.iluria.commons.gateway.FileManagerGateway;
import com.iluria.commons.gateway.FileManagerS3Gateway;
import com.iluria.commons.usecase.fileManager.CreateFileUseCase;
import com.iluria.auth.domain.User;
import com.iluria.auth.gateway.UserGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/store/themes")
@RequiredArgsConstructor
public class ThemeController {

    private final FindStoreSpecificThemesUseCase findStoreSpecificThemesUseCase;
    private final FindThemeByIdUseCase findThemeByIdUseCase;
    private final SaveThemeUseCase saveThemeUseCase;
    private final DeleteThemeUseCase deleteThemeUseCase;
    private final ActivateThemeUseCase activateThemeUseCase;
    private final GetActiveThemeUseCase getActiveThemeUseCase;
    private final ApplyThemeTemplateUseCase applyThemeTemplateUseCase;
    private final ThemeTemplateManagerUseCase themeTemplateManagerUseCase;
    private final SwitchThemeUseCase switchThemeUseCase;
    private final SyncThemeTemplatesUseCase syncThemeTemplatesUseCase;
    private final FindDefaultThemesUseCase findDefaultThemesUseCase;
    private final CopyDefaultThemeUseCase copyDefaultThemeUseCase;
    private final DuplicateThemeUseCase duplicateThemeUseCase;
    private final InitializeDefaultThemesUseCase initializeDefaultThemesUseCase;
    private final CleanupOrphanedFilesUseCase cleanupOrphanedFilesUseCase;
    private final ThemeProgressService themeProgressService;
    private final ThemeBackupService themeBackupService;
    private final FindDomainUrlUseCase findDomainUrlUseCase;
    private final FileManagerGateway fileManagerGateway;
    private final FileManagerS3Gateway fileManagerS3Gateway;
    private final CreateFileUseCase createFileUseCase;
    private final ThemeDTOMapper mapper;
    private final AsyncSitePublishingProgressService asyncSitePublishingProgressService;
    private final UserGateway userGateway;

    @GetMapping
    public ResponseEntity<List<ThemeDTO>> getThemes(
            @RequestParam(required = false) String categoryId) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());

        if (categoryId != null && !categoryId.trim().isEmpty()) {
            ULID categoryULID = ULID.fromString(categoryId);
            // Usar findStoreSpecificThemesUseCase para não incluir temas padrão na galeria
            List<Theme> themes = findStoreSpecificThemesUseCase.executeWithCategory(storeId, categoryULID);
            return ResponseEntity.ok(mapper.toDTOList(themes));
        }

        // Usar findStoreSpecificThemesUseCase para não incluir temas padrão na galeria
        List<Theme> themes = findStoreSpecificThemesUseCase.execute(storeId);
        return ResponseEntity.ok(mapper.toDTOList(themes));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ThemeDTO> getTheme(@PathVariable String id) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeId = ULID.fromString(id);
        Theme theme = findThemeByIdUseCase.execute(themeId, storeId);
        return ResponseEntity.ok(mapper.toDTO(theme));
    }

    @GetMapping("/active")
    public ResponseEntity<ThemeDTO> getActiveTheme() {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        Theme theme = getActiveThemeUseCase.execute(storeId);
        if (theme == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
        return ResponseEntity.ok(mapper.toDTO(theme));
    }

    @PostMapping
    public ResponseEntity<ThemeDTO> createTheme(@Valid @RequestBody CreateThemeRequestDTO request) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        Theme theme = mapper.toModel(request, storeId);
        Theme savedTheme = saveThemeUseCase.execute(theme);
        return ResponseEntity.status(HttpStatus.CREATED).body(mapper.toDTO(savedTheme));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ThemeDTO> updateTheme(
            @PathVariable String id,
            @Valid @RequestBody UpdateThemeRequestDTO request) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeId = ULID.fromString(id);

        Theme existingTheme = findThemeByIdUseCase.execute(themeId, storeId);
        mapper.updateModelFromDTO(request, existingTheme);

        Theme updatedTheme = saveThemeUseCase.execute(existingTheme);
        return ResponseEntity.ok(mapper.toDTO(updatedTheme));
    }

    @PostMapping("/{id}/activate")
    public ResponseEntity<ThemeDTO> activateTheme(@PathVariable String id) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeId = ULID.fromString(id);

        Theme theme = findThemeByIdUseCase.execute(themeId, storeId);

        try {
            themeProgressService.sendThemeChangeStarted(storeId, theme.getName());

            Theme currentTheme = getActiveThemeUseCase.execute(storeId);
            
            if (currentTheme != null && !currentTheme.getId().equals(themeId)) {
                themeBackupService.createAutomaticBackup(storeId, currentTheme.getId(), "THEME_CHANGE");
            }

            themeProgressService.sendTemplatesApplying(storeId);
            
            Theme activatedTheme;

            // NOVA LÓGICA: Sempre sincronizar templates quando o tema tem configuração válida
            if (theme.hasValidTemplateConfiguration()) {
                
                if (currentTheme != null && !theme.getIsDefault()) {
                    // Caso 1: Mudança de tema (tema atual existe e novo tema não é padrão)
                    activatedTheme = switchThemeUseCase.execute(currentTheme.getId(), themeId, storeId);
                } else {
                    // Caso 2: Primeiro tema OU tema padrão - Ativar + Sincronizar manualmente
                    
                    // Primeiro ativa o tema
                    activatedTheme = activateThemeUseCase.execute(themeId, storeId);
                    
                    // Depois força a sincronização dos templates
                    try {
                        SyncThemeTemplatesUseCase.SyncOptions syncOptions = SyncThemeTemplatesUseCase.SyncOptions.builder()
                                .environment(FileManagerEnum.DEVELOP)
                                .forceUpdate(true)
                                .removeOrphaned(true)
                                .build();
                        
                        SyncThemeTemplatesUseCase.SyncResult syncResult = syncThemeTemplatesUseCase.execute(themeId, storeId, syncOptions);
                    } catch (Exception syncError) {
                        // Não falha a ativação por causa da sincronização
                    }
                }
            } else {
                // Caso 3: Tema sem templates válidos - apenas ativar
                activatedTheme = activateThemeUseCase.execute(themeId, storeId);
            }
            
            themeProgressService.sendThemeChangeCompleted(storeId, activatedTheme.getName());

            return ResponseEntity.ok(mapper.toDTO(activatedTheme));
        } catch (Exception e) {
            themeProgressService.sendThemeChangeError(storeId, e.getMessage());
            throw e;
        }
    }

    @PostMapping("/{id}/duplicate")
    public ResponseEntity<ThemeDTO> duplicateTheme(@PathVariable String id) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeId = ULID.fromString(id);

        Theme duplicatedTheme = duplicateThemeUseCase.execute(themeId, storeId);
        return ResponseEntity.status(HttpStatus.CREATED).body(mapper.toDTO(duplicatedTheme));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTheme(@PathVariable String id) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeId = ULID.fromString(id);
        deleteThemeUseCase.execute(themeId, storeId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/css")
    public ResponseEntity<String> getThemeCss(@PathVariable String id) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeId = ULID.fromString(id);
        Theme theme = findThemeByIdUseCase.execute(themeId, storeId);
        String cssContent = theme.generateCssContent();
        return ResponseEntity.ok()
                .header("Content-Type", "text/css")
                .body(cssContent);
    }

    @PostMapping("/{id}/apply-template")
    public ResponseEntity<ThemeDTO> applyThemeTemplate(@PathVariable String id) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeId = ULID.fromString(id);
        Theme appliedTheme = applyThemeTemplateUseCase.execute(themeId, storeId);
        return ResponseEntity.ok(mapper.toDTO(appliedTheme));
    }

    @PostMapping("/{id}/switch")
    public ResponseEntity<ThemeDTO> switchTheme(@PathVariable String id) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID newThemeId = ULID.fromString(id);

        Theme currentTheme = getActiveThemeUseCase.execute(storeId);
        ULID currentThemeId = currentTheme != null ? currentTheme.getId() : null;

        if (currentThemeId == null) {
            Theme appliedTheme = applyThemeTemplateUseCase.execute(newThemeId, storeId);
            return ResponseEntity.ok(mapper.toDTO(appliedTheme));
        }

        Theme switchedTheme = switchThemeUseCase.execute(currentThemeId, newThemeId, storeId);
        return ResponseEntity.ok(mapper.toDTO(switchedTheme));
    }

    @PostMapping("/{id}/templates/upload")
    public ResponseEntity<ThemeDTO> uploadTemplateFiles(
            @PathVariable String id,
            @RequestBody Map<String, String> templateFiles,
            @RequestParam(required = false, defaultValue = "1.0.0") String templateVersion) {
        ULID themeId = ULID.fromString(id);

        Theme updatedTheme = themeTemplateManagerUseCase.uploadTemplateFiles(themeId, templateFiles, templateVersion);
        return ResponseEntity.ok(mapper.toDTO(updatedTheme));
    }

    @PostMapping("/{id}/templates/sync")
    public ResponseEntity<ThemeDTO> syncTemplateFiles(@PathVariable String id) {
        ULID themeId = ULID.fromString(id);

        Theme syncedTheme = themeTemplateManagerUseCase.syncTemplateFiles(themeId);
        return ResponseEntity.ok(mapper.toDTO(syncedTheme));
    }

    @GetMapping("/{id}/templates")
    public ResponseEntity<Map<String, String>> getTemplateFiles(@PathVariable String id) {
        ULID themeId = ULID.fromString(id);

        Map<String, String> templateFiles = themeTemplateManagerUseCase.downloadTemplateFiles(themeId);
        return ResponseEntity.ok(templateFiles);
    }

    @DeleteMapping("/{id}/templates")
    public ResponseEntity<ThemeDTO> removeTemplateFiles(@PathVariable String id) {
        ULID themeId = ULID.fromString(id);

        Theme updatedTheme = themeTemplateManagerUseCase.removeTemplateFiles(themeId);
        return ResponseEntity.ok(mapper.toDTO(updatedTheme));
    }

    @GetMapping("/{id}/template-manifest")
    public ResponseEntity<String> getTemplateManifest(@PathVariable String id) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeId = ULID.fromString(id);

        Theme theme = findThemeByIdUseCase.execute(themeId, storeId);
        String manifest = theme.generateTemplateManifest();

        if (manifest == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }

        return ResponseEntity.ok()
                .header("Content-Type", "text/plain")
                .body(manifest);
    }

    @GetMapping("/{id}/template-status")
    public ResponseEntity<Map<String, Object>> getTemplateStatus(@PathVariable String id) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeId = ULID.fromString(id);

        Theme theme = findThemeByIdUseCase.findById(themeId);

        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }

        if (!Boolean.TRUE.equals(theme.getIsDefault())
                && (theme.getStoreId() == null || !storeId.equals(theme.getStoreId()))) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }

        Map<String, Object> status = Map.of(
                "hasTemplateFiles", theme.getHasTemplateFiles() != null ? theme.getHasTemplateFiles() : false,
                "templateS3Path", theme.getTemplateS3Path() != null ? theme.getTemplateS3Path() : "",
                "templateVersion", theme.getTemplateVersion() != null ? theme.getTemplateVersion() : "",
                "templateFileCount", theme.getTemplateFiles() != null ? theme.getTemplateFiles().size() : 0,
                "templateFiles", theme.getTemplateFilesList(),
                "templateUpdatedAt", theme.getTemplateUpdatedAt(),
                "templateChecksum", theme.getTemplateChecksum() != null ? theme.getTemplateChecksum() : "");

        return ResponseEntity.ok(status);
    }

    @GetMapping("/defaults")
    public ResponseEntity<List<ThemeDTO>> getDefaultThemes() {
        List<Theme> defaultThemes = findDefaultThemesUseCase.execute();
        List<ThemeDTO> themeDTOs = defaultThemes.stream()
                .map(mapper::toDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(themeDTOs);
    }

    @PostMapping("/initialize-defaults")
    public ResponseEntity<List<ThemeDTO>> initializeDefaultThemes() {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        List<Theme> initializedThemes = initializeDefaultThemesUseCase.execute(storeId);
        List<ThemeDTO> themeDTOs = initializedThemes.stream()
                .map(mapper::toDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(themeDTOs);
    }

    @PostMapping("/force-initialize-defaults")
    public ResponseEntity<List<ThemeDTO>> forceInitializeDefaultThemes() {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        List<Theme> initializedThemes = initializeDefaultThemesUseCase.forceInitialize(storeId);
        List<ThemeDTO> themeDTOs = initializedThemes.stream()
                .map(mapper::toDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(themeDTOs);
    }

    @PostMapping("/defaults/{id}/use")
    public ResponseEntity<ThemeDTO> useDefaultTheme(
            @PathVariable String id,
            @RequestHeader("X-Store-Id") String storeIdHeader) {
        ULID defaultThemeId = ULID.fromString(id);
        ULID storeId = ULID.fromString(storeIdHeader);

        List<Theme> defaultThemes = findDefaultThemesUseCase.execute();

        Theme defaultTheme = defaultThemes.stream()
                .filter(theme -> {
                    boolean matches = theme.getId().equals(defaultThemeId);
                    return matches;
                })
                .findFirst()
                .orElse(null);

        if (defaultTheme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }

        try {
            themeProgressService.sendThemeChangeStarted(storeId, defaultTheme.getName());

            // Criar backup do tema atual antes da mudança (não crítico)
            try {
                Theme currentTheme = getActiveThemeUseCase.execute(storeId);
                if (currentTheme != null) {
                    themeBackupService.createAutomaticBackup(storeId, currentTheme.getId(), "THEME_CHANGE");
                } else {
                }
            } catch (Exception backupError) {
                // Não interromper o processo por causa do backup - continuar aplicando o tema
            }

            themeProgressService.sendCustomProgress(storeId, "COPYING_THEME",
                    "Copiando tema padrão para sua loja...", 25);

            Theme copiedTheme = copyDefaultThemeUseCase.execute(defaultThemeId, storeId, null, null);

            // O CopyDefaultThemeUseCase já copia todas as configurações necessárias com
            // path único
            // Não precisamos sobrescrever o templateS3Path que foi gerado corretamente

            // Aplicar os templates do tema (baixar do S3 e copiar para FileManager)
            themeProgressService.sendTemplatesDownloading(storeId);
            Theme themeWithTemplates = applyThemeTemplateUseCase.execute(copiedTheme.getId(), storeId);

            // Sincronizar com S3
            themeProgressService.sendS3Syncing(storeId);

            // Ativar o tema copiado
            themeProgressService.sendTemplatesApplying(storeId);
            Theme activatedTheme = activateThemeUseCase.execute(themeWithTemplates.getId(), storeId);

            themeProgressService.sendThemeChangeCompleted(storeId, activatedTheme.getName());

            ThemeDTO themeDTO = mapper.toDTO(activatedTheme);
            return ResponseEntity.ok(themeDTO);
        } catch (Exception e) {
            themeProgressService.sendThemeChangeError(storeId, e.getMessage());
            throw e; // Re-throw para manter o comportamento original
        }
    }

    @GetMapping("/backups")
    public ResponseEntity<List<Map<String, Object>>> getBackups() {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());

        List<ThemeBackup> backups = themeBackupService.listBackupsByStore(storeId);

        List<Map<String, Object>> backupDTOs = backups.stream()
                .map(backup -> {
                    Map<String, Object> backupMap = new HashMap<>();
                    backupMap.put("id", backup.getId().toString());
                    backupMap.put("themeId", backup.getThemeId().toString());
                    backupMap.put("themeName", backup.getThemeName());
                    backupMap.put("backupReason", backup.getBackupReason());
                    backupMap.put("createdAt", backup.getCreatedAt());
                    backupMap.put("expiresAt", backup.getExpiresAt());
                    backupMap.put("fileCount", backup.getFileCount());
                    backupMap.put("backupSizeBytes", backup.getBackupSizeBytes());
                    backupMap.put("restorationCount", backup.getRestorationCount());
                    backupMap.put("lastRestoredAt", backup.getLastRestoredAt());
                    backupMap.put("description", backup.getBackupDescription());
                    return backupMap;
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(backupDTOs);
    }

    @GetMapping("/{themeId}/backups")
    public ResponseEntity<List<Map<String, Object>>> getBackupsByTheme(@PathVariable String themeId) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeULID = ULID.fromString(themeId);

        List<ThemeBackup> backups = themeBackupService.listBackupsByTheme(storeId, themeULID);

        List<Map<String, Object>> backupDTOs = backups.stream()
                .map(backup -> {
                    Map<String, Object> backupMap = new HashMap<>();
                    backupMap.put("id", backup.getId().toString());
                    backupMap.put("themeId", backup.getThemeId().toString());
                    backupMap.put("themeName", backup.getThemeName());
                    backupMap.put("backupReason", backup.getBackupReason());
                    backupMap.put("createdAt", backup.getCreatedAt());
                    backupMap.put("expiresAt", backup.getExpiresAt());
                    backupMap.put("fileCount", backup.getFileCount());
                    backupMap.put("backupSizeBytes", backup.getBackupSizeBytes());
                    backupMap.put("restorationCount", backup.getRestorationCount());
                    backupMap.put("lastRestoredAt", backup.getLastRestoredAt());
                    backupMap.put("description", backup.getBackupDescription());
                    return backupMap;
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(backupDTOs);
    }

    @PostMapping("/{themeId}/backup")
    public ResponseEntity<Map<String, Object>> createManualBackup(@PathVariable String themeId) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID themeULID = ULID.fromString(themeId);

        try {

            ThemeBackup backup = themeBackupService.createManualBackup(storeId, themeULID);

            if (backup == null) {
                return ResponseEntity.badRequest().body(
                        Map.of("error",
                                "Não foi possível criar backup. Tema pode não ter templates ou não foi encontrado."));
            }

            // Criar DTO de resposta
            Map<String, Object> backupDTO = new HashMap<>();
            backupDTO.put("id", backup.getId().toString());
            backupDTO.put("themeId", backup.getThemeId().toString());
            backupDTO.put("themeName", backup.getThemeName());
            backupDTO.put("backupReason", backup.getBackupReason());
            backupDTO.put("createdAt", backup.getCreatedAt());
            backupDTO.put("fileCount", backup.getFileCount());
            backupDTO.put("backupSizeBytes", backup.getBackupSizeBytes());
            backupDTO.put("description", backup.getBackupDescription());

            return ResponseEntity.ok(backupDTO);

        } catch (Exception e) {
            return ResponseEntity.status(500).body(
                    Map.of("error", "Erro interno ao criar backup: " + e.getMessage()));
        }
    }

    @PostMapping("/backups/{backupId}/restore")
    public ResponseEntity<Map<String, Object>> restoreBackup(@PathVariable String backupId) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID backupULID = ULID.fromString(backupId);

        // First check if backup exists and is valid
        Optional<ThemeBackup> optionalBackup = themeBackupService.findBackupById(backupULID);
        if (optionalBackup.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of(
                    "error", "Backup não encontrado."));
        }
        
        ThemeBackup backup = optionalBackup.get();
        if (!backup.getStoreId().equals(storeId)) {
            return ResponseEntity.badRequest().body(Map.of(
                    "error", "Backup não pertence a esta loja."));
        }
        
        if (!backup.isValid()) {
            return ResponseEntity.badRequest().body(Map.of(
                    "error", backup.getValidationFailureReason()));
        }

        boolean success = themeBackupService.restoreBackup(storeId, backupULID);

        if (success) {
            return ResponseEntity.ok(Map.of(
                    "message", "Backup restaurado com sucesso",
                    "backupId", backupId));
        } else {
            return ResponseEntity.badRequest().body(Map.of(
                    "error",
                    "Falha na restauração do backup. Verifique se os arquivos de backup estão disponíveis no S3."));
        }
    }

    @DeleteMapping("/backups/{backupId}")
    public ResponseEntity<Map<String, Object>> deleteBackup(@PathVariable String backupId) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        ULID backupULID = ULID.fromString(backupId);

        boolean success = themeBackupService.deleteBackup(storeId, backupULID);

        if (success) {
            return ResponseEntity.ok(Map.of(
                    "message", "Backup removido com sucesso",
                    "backupId", backupId));
        } else {
            return ResponseEntity.badRequest().body(Map.of(
                    "error", "Não foi possível remover o backup."));
        }
    }

    @GetMapping("/backups/stats")
    public ResponseEntity<Map<String, Object>> getBackupStats() {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());

        Map<String, Object> stats = themeBackupService.getBackupStats(storeId);

        return ResponseEntity.ok(stats);
    }

    @PostMapping("/cleanup/orphaned-files")
    public ResponseEntity<Map<String, Object>> cleanupOrphanedFiles(
            @RequestParam(required = false, defaultValue = "DEVELOP") String environment) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());

        try {
            FileManagerEnum env = FileManagerEnum.valueOf(environment.toUpperCase());

            // Executar limpeza de arquivos órfãos
            int orphanedRemoved = cleanupOrphanedFilesUseCase.removeOrphanedFiles(storeId, env);

            Map<String, Object> result = Map.of(
                    "success", true,
                    "message", "Limpeza de arquivos órfãos concluída",
                    "orphanedFilesRemoved", orphanedRemoved,
                    "environment", environment);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "error", "Erro durante limpeza: " + e.getMessage()));
        }
    }

    @PostMapping("/cleanup/full")
    public ResponseEntity<Map<String, Object>> fullCleanup(
            @RequestParam(required = false, defaultValue = "DEVELOP") String environment) {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());

        try {
            FileManagerEnum env = FileManagerEnum.valueOf(environment.toUpperCase());

            // Buscar tema ativo atual
            Theme activeTheme = getActiveThemeUseCase.execute(storeId);
            if (activeTheme == null) {
                return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "error", "Nenhum tema ativo encontrado"));
            }

            // Executar limpeza completa
            int totalRemoved = cleanupOrphanedFilesUseCase.executeFullCleanup(storeId, activeTheme.getId(), env);

            Map<String, Object> result = Map.of(
                    "success", true,
                    "message", "Limpeza completa concluída",
                    "totalFilesRemoved", totalRemoved,
                    "activeTheme", activeTheme.getName(),
                    "environment", environment);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "error", "Erro durante limpeza: " + e.getMessage()));
        }
    }

    @GetMapping("/publish/status")
    public ResponseEntity<Map<String, Object>> getPublishStatus() {

        try {
            Map<String, Object> status = new HashMap<>();

            // Verificar domínios conectados
            List<DomainUrl> domains = findDomainUrlUseCase.byStoreId();
            List<DomainUrl> connectedDomains = domains.stream()
                    .filter(domain -> "CONNECTED".equals(domain.getStatus()))
                    .collect(Collectors.toList());

            boolean hasValidDomain = !connectedDomains.isEmpty();

            // Verificar SSL para domínios conectados (mock por enquanto)
            boolean hasSSL = hasValidDomain; // Por enquanto, assume SSL válido se tem domínio válido

            // Verificar se há diferenças entre dev e prod (mock por enquanto)
            boolean isOutdated = true;

            status.put("canPublish", hasValidDomain && hasSSL && isOutdated);
            status.put("hasValidDomain", hasValidDomain);
            status.put("hasSSL", hasSSL);
            status.put("isOutdated", isOutdated);
            status.put("domains", connectedDomains.stream()
                    .map(domain -> {
                        Map<String, Object> domainMap = new HashMap<>();
                        domainMap.put("name", domain.getName());
                        domainMap.put("status", domain.getStatus());
                        domainMap.put("type", "DOMAIN");
                        return domainMap;
                    })
                    .collect(Collectors.toList()));

            if (!hasValidDomain) {
                status.put("message", "Configure um domínio válido em Configurações > Domínios");
            } else if (!hasSSL) {
                status.put("message", "Aguardando instalação do certificado SSL");
            } else if (!isOutdated) {
                status.put("message", "Sua loja está atualizada e publicada");
            } else {
                status.put("message", "Pronto para publicar as alterações");
            }

            return ResponseEntity.ok(status);

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                    "error", "Erro ao verificar status de publicação: " + e.getMessage()));
        }
    }

    @PostMapping("/publish")
    public ResponseEntity<Map<String, Object>> publishToProduction() {
        ULID storeId = ULID.fromString(AuthorizationContext.getStoreId());
        String userId = AuthorizationContext.getUserIdFromSubject();
        User user = userGateway.findById(io.github.jaspeen.ulid.ULID.fromString(userId));

        try {
            // Verificar se pode publicar ANTES de iniciar o processo assíncrono
            ResponseEntity<Map<String, Object>> statusResponse = getPublishStatus();
            Map<String, Object> status = statusResponse.getBody();

            if (status == null || !(Boolean) status.get("canPublish")) {
                return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "error", "Não é possível publicar no momento. Verifique domínios e SSL."));
            }

            // Iniciar processo assíncrono APENAS se pode publicar
            asyncSitePublishingProgressService.publishSiteDataAsync(user.getEmail(), storeId);

            // Implementar sincronização real DEVELOP -> PRODUCTION

            // Executar sincronização dos arquivos HTML
            syncHtmlFilesFromDevToProd(storeId);

            Map<String, Object> result = Map.of(
                    "success", true,
                    "message", "Site publicado com sucesso!",
                    "publishedAt", LocalDateTime.now(),
                    "storeId", storeId.toString());

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                    "success", false,
                    "error", "Erro durante publicação: " + e.getMessage()));
        }
    }

    private void syncHtmlFilesFromDevToProd(ULID storeId) throws Exception {

        // 1. Buscar arquivos HTML do ambiente DEVELOP
        List<FileManager> devHtmlFiles = fileManagerGateway
                .findByStoreIdAndEnvironment(storeId, FileManagerEnum.DEVELOP)
                .stream()
                .filter(file -> file.getType() == FileManagerEnum.ARCHIVE && file.getName().endsWith(".html"))
                .collect(Collectors.toList());

        if (devHtmlFiles.isEmpty()) {
            return;
        }

        // 2. Buscar arquivos HTML existentes no ambiente PRODUCTION (para backup)
        List<FileManager> prodHtmlFiles = fileManagerGateway
                .findByStoreIdAndEnvironment(storeId, FileManagerEnum.PRODUCTION)
                .stream()
                .filter(file -> file.getType() == FileManagerEnum.ARCHIVE && file.getName().endsWith(".html"))
                .collect(Collectors.toList());

        // 3. Criar backup dos arquivos de produção (em memória)
        Map<String, String> prodBackup = new HashMap<>();
        for (FileManager prodFile : prodHtmlFiles) {
            try {
                byte[] contentBytes = fileManagerS3Gateway.getFileContent(prodFile);
                if (contentBytes != null) {
                    prodBackup.put(prodFile.getName(),
                            new String(contentBytes, java.nio.charset.StandardCharsets.UTF_8));
                }
            } catch (Exception e) {
            }
        }

        // 4. Copiar arquivos de DEVELOP para PRODUCTION
        List<String> copiedFiles = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();

        for (FileManager devFile : devHtmlFiles) {
            try {
                // Baixar conteúdo do arquivo de DEVELOP
                byte[] contentBytes = fileManagerS3Gateway.getFileContent(devFile);
                if (contentBytes == null) {
                    failedFiles.add(devFile.getName());
                    continue;
                }

                String content = new String(contentBytes, java.nio.charset.StandardCharsets.UTF_8);

                // Criar arquivo no ambiente PRODUCTION
                createFileUseCase.execute(devFile.getName(), content, null, "prod", storeId);
                copiedFiles.add(devFile.getName());

            } catch (Exception e) {
                failedFiles.add(devFile.getName());
            }
        }

        // 5. Verificar se a cópia foi bem-sucedida
        if (!failedFiles.isEmpty()) {
            throw new RuntimeException("Falha na sincronização de arquivos: " + String.join(", ", failedFiles));
        }

        // 6. Remover arquivos antigos de PRODUCTION apenas após confirmar sucesso
        for (FileManager oldProdFile : prodHtmlFiles) {
            try {
                fileManagerGateway.deleteById(oldProdFile.getId());
            } catch (Exception e) {
                // Não falha a operação por causa disso
            }
        }

    }
}