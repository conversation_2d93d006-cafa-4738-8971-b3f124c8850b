package com.iluria.exception;

import com.iluria.commons.ErrorMessage;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum StoreErrorMessageEnum implements ErrorMessage {

    // Auth errors (100xx)
    UNEXPECTED_ERROR("10001"),
    INVALID_JWT_TOKEN("10002"),
    INVALID_MFA_TOKEN("10003"),
    USER_NOT_FOUND("10004"),
    RECOVERY_ALREADY_REQUESTED("10005"),
    EMAIL_NOT_FOUND("10006"),
    INVALID_PASSWORD_RECOVERY_DATA("10007"),
    INVALID_PASSWORD_RECOVERY_ID("10008"),
    INVALID_PASSWORD_RECOVERY_CODE("10009"),
    EXPIRED_PASSWORD_RECOVERY_CODE("10010"),
    USED_PASSWORD_RECOVERY_CODE("10011"),
    INVALID_CURRENT_PASSWORD("10012"),
    PASSWORDS_DO_NOT_MATCH("10013"),
    INVALID_PASSWORD_CHANGE_DATA("10014"),
    MFA_NOT_ENABLED("10015"),
    INVALID_SIGNUP_CODE("10016"),
    SIGNUP_ALREADY_REQUESTED("10017"),
    INVALID_SIGNUP_REQUEST("10018"),
    EXPIRED_SIGNUP_REQUEST("10019"),
    EMAIL_ALREADY_REGISTERED("10020"),
    SIGNUP_USER_CREATION_FAILED("10021"),

    // Store general errors (300xx)
    STORE_NOT_FOUND("30001"),
    COUPON_NOT_FOUND("30003"),
    COUPON_CODE_ALREADY_EXISTS("30004"),
    INVALID_COUPON_DATA("30005"),
    INVALID_CODE("30006"),
    INVALID_START_DATE("30007"),
    INVALID_END_DATE("30008"),
    INVALID_DISCOUNT_VALUE("30009"),
    INVALID_USAGE_LIMIT("30010"),
    INVALID_USAGE_LIMIT_PER_CUSTOMER("30011"),
    ADDRESS_NOT_FOUND("30012"),
    CUSTOMER_NOT_BELONG_TO_STORE("30013"),
    ZIPCODE_NOT_FOUND("30014"),
    SHIPPING_CONFIG_NOT_FOUND("30015"),
    CATEGORY_INFO_NOT_FOUND("30016"),
    CATEGORY_IMAGE_UPLOAD_FAILED("30017"),
    CATEGORY_IMAGE_DELETE_FAILED("30018"),
    INVALID_CATEGORY_IMAGE_TYPE("30019"),
    STORE_STATUS_NOT_FOUND("30020"),

    // Store settings errors (301xx)
    INVALID_STORE_ID("30101"),
    URL_REDIRECT_NOT_FOUND("30102"),
    URL_REDIRECT_ALREADY_EXISTS("30103"),
    STORE_SEO_SETTINGS_NOT_FOUND("30104"),
    STORE_SEO_SETTINGS_ALREADY_EXISTS("30105"),
    STORE_BRAND_ASSETS_NOT_FOUND("30133"),
    SOCIAL_MEDIA_SETTINGS_UPLOAD_FAILED("30106"),
    SOCIAL_MEDIA_SETTINGS_DELETE_FAILED("30107"),
    SOCIAL_MEDIA_SETTINGS_NOT_FOUND("30108"),
    INVALID_SEO_SETTINGS_DATA("30109"),
    PAGE_NOT_FOUND("30110"),
    FILE_NOT_FOUND("30111"),
    MAX_SUBFOLDERS_REACHED("30112"),
    CANNOT_CREATE_FOLDER_INSIDE_FILE("30113"),
    ENVIRONMENT_MISMATCH("30114"),
    FILE_UPLOAD_FAILED("30115"),
    FILE_TOO_LARGE("30116"),
    INVALID_PARENT_FOLDER("30117"),
    INVALID_FILE_NAME("30118"),
    INVALID_FILE_CONTENT("30119"),
    FILE_UPDATE_FAILED("30120"),
    FOLDER_NOT_FOUND("30121"),
    FOLDER_MOVE_FAILED("30122"),
    FILE_DELETE_FAILED("30123"),
    FILE_PARENT_NOT_FOUND("30124"),
    INVALID_FILE_TYPE("30125"),
    FILE_PATH_BUILD_ERROR("30126"),
    FILE_MOVE_FAILED("30127"),
    DOMAIN_URL_NOT_FOUND("30128"),
    DOMAIN_URL_ALREADY_EXISTS("30129"),
    DOMAIN_URL_LIMIT_EXCEEDED("30130"),
    DOMAIN_URL_LAST_MAIN("30131"),
    SEO_IMAGE_TOO_LARGE("30132"),
    INVALID_STORE_STATUS_DATA("30133"),
    STORE_INITIALIZATION_ERROR("30134"),

    // Customer Export errors (301xx)
    CUSTOMER_EXPORT_JOB_NOT_FOUND("30140"),
    CUSTOMER_EXPORT_MAX_JOBS_EXCEEDED("30141"),
    CUSTOMER_EXPORT_JOB_NOT_COMPLETED("30142"),
    CUSTOMER_EXPORT_FILE_NOT_FOUND("30143"),
    CUSTOMER_EXPORT_S3_UPLOAD_FAILED("30144"),
    CUSTOMER_EXPORT_S3_DOWNLOAD_FAILED("30145"),
    CUSTOMER_EXPORT_S3_DELETE_FAILED("30146"),
    CUSTOMER_EXPORT_LIFECYCLE_CONFIG_FAILED("30147"),
    CUSTOMER_EXPORT_TOKEN_INVALID("30148"),
    CUSTOMER_EXPORT_TOKEN_EXPIRED("30149"),
    CUSTOMER_EXPORT_UNAUTHORIZED("30150"),

    // Customer Import errors (301xx)
    CUSTOMER_IMPORT_JOB_NOT_FOUND("30151"),
    CUSTOMER_IMPORT_MAX_JOBS_EXCEEDED("30152"),
    CUSTOMER_IMPORT_FILE_EMPTY("30153"),
    CUSTOMER_IMPORT_INVALID_FILE_TYPE("30154"),
    CUSTOMER_IMPORT_FILE_TOO_LARGE("30155"),
    CUSTOMER_IMPORT_PARSE_ERROR("30156"),
    CUSTOMER_IMPORT_INVALID_MAPPING("30157"),
    CUSTOMER_IMPORT_S3_ERROR("30158"),

    // Product Export errors (301xx)
    PRODUCT_EXPORT_JOB_NOT_FOUND("30160"),
    PRODUCT_EXPORT_MAX_JOBS_EXCEEDED("30161"),
    PRODUCT_EXPORT_JOB_NOT_COMPLETED("30162"),
    PRODUCT_EXPORT_FILE_NOT_FOUND("30163"),
    PRODUCT_EXPORT_S3_UPLOAD_FAILED("30164"),
    PRODUCT_EXPORT_S3_DOWNLOAD_FAILED("30165"),
    PRODUCT_EXPORT_S3_DELETE_FAILED("30166"),
    PRODUCT_EXPORT_TOKEN_INVALID("30167"),
    PRODUCT_EXPORT_TOKEN_EXPIRED("30168"),
    PRODUCT_EXPORT_UNAUTHORIZED("30169"),

    // Product Import errors (301xx)
    PRODUCT_IMPORT_JOB_NOT_FOUND("30170"),
    PRODUCT_IMPORT_MAX_JOBS_EXCEEDED("30171"),
    PRODUCT_IMPORT_FILE_EMPTY("30172"),
    PRODUCT_IMPORT_INVALID_FILE_TYPE("30173"),
    PRODUCT_IMPORT_FILE_TOO_LARGE("30174"),
    PRODUCT_IMPORT_PARSE_ERROR("30175"),
    PRODUCT_IMPORT_INVALID_MAPPING("30176"),
    PRODUCT_IMPORT_S3_ERROR("30177"),

    // Order errors (302xx)
    ORDER_NOT_FOUND("30201"),
    ORDER_EMPTY_ITEMS("30202"),
    CUSTOMER_NOT_FOUND("30203"),
    ORIGIN_CEP_NOT_FOUND("30204"),
    ORIGIN_CEP_LIMIT_EXCEEDED("30205"),
    ORIGIN_CEP_INVALID("30206"),

    // Promotion errors (303xx)
    PROMOTION_NOT_FOUND("30301"),
    FAILED_TO_SAVE_PROMOTION("30302"),
    FAILED_TO_DELETE_PROMOTION("30303"),
    FAILED_TO_FETCH_PROMOTIONS("30304"),
    FAILED_TO_FETCH_ACTIVE_PROMOTIONS("30305"),

    // File system errors (304xx)
    FAILED_TO_SYNCHRONIZE_FILES("30401"),
    PATH_TRAVERSAL_DETECTED("30402"),
    FAILED_TO_CREATE_DIRECTORY("30403"),
    ZIP_BOMB_DETECTED("30404"),
    TOO_MANY_FILES_IN_ZIP("30405"),
    EXTRACTION_SIZE_LIMIT_EXCEEDED("30406"),

    // Blog Category errors (305xx)
    BLOG_CATEGORY_NOT_FOUND("30501"),
    INVALID_BLOG_CATEGORY_NAME("30502"),
    INVALID_BLOG_CATEGORY_SLUG("30503"),
    BLOG_CATEGORY_SLUG_ALREADY_EXISTS("30504"),
    INVALID_META_TITLE_LENGTH("30505"),
    INVALID_META_DESCRIPTION_LENGTH("30506"),
    BLOG_CATEGORY_IMAGE_UPLOAD_FAILED("30507"),
    BLOG_CATEGORY_IMAGE_DELETE_FAILED("30508"),

    // Community errors (306xx)
    CATEGORY_NOT_FOUND("30601"),
    CATEGORY_ALREADY_EXISTS("30602"),
    GROUP_NOT_FOUND("30603"),
    GROUP_ALREADY_EXISTS("30604"),
    CATEGORY_HAS_GROUPS("30605"),

    // Social Links errors (307xx)
    SOCIAL_LINKS_NOT_FOUND("30701"),
    SOCIAL_LINKS_INSTAGRAM_URL_REQUIRED("30702"),
    SOCIAL_LINKS_twitter_url_REQUIRED("30703"),
    SOCIAL_LINKS_FACEBOOK_URL_REQUIRED("30704"),
    SOCIAL_LINKS_TIKTOK_URL_REQUIRED("30705"),
    SOCIAL_LINKS_INSTAGRAM_URL_INVALID("30706"),
    SOCIAL_LINKS_twitter_url_INVALID("30707"),
    SOCIAL_LINKS_FACEBOOK_URL_INVALID("30708"),
    SOCIAL_LINKS_TIKTOK_URL_INVALID("30709"),
    SOCIAL_LINKS_SAVE_ERROR("30710"),

    // CEP Address API errors (308xx)
    CEP_API_NOT_FOUND("30801"),

       // Blog Post errors (309xx)
    BLOG_POST_NOT_FOUND("30901"),
    INVALID_BLOG_POST_TITLE("30902"),
    INVALID_BLOG_POST_SLUG("30903"),
    BLOG_POST_SLUG_ALREADY_EXISTS("30904"),
    BLOG_POST_CATEGORY_NOT_FOUND("30905"),
    INVALID_BLOG_POST_CONTENT("30906"),
    INVALID_BLOG_POST_META_TITLE_LENGTH("30907"),
    INVALID_BLOG_POST_META_DESCRIPTION_LENGTH("30908"),
    BLOG_POST_IMAGE_UPLOAD_FAILED("30909"),
    BLOG_POST_IMAGE_DELETE_FAILED("30910"),
    FILE_CANNOT_BE_EMPTY("30911"),
    USER_PROFILE_IMAGE_UPLOAD_FAILED("30912"),
    USER_PROFILE_IMAGE_DELETE_FAILED("30913"),

    // Customer Photo errors (3100x)
    CUSTOMER_PHOTO_UPLOAD_FAILED("31001"),
    CUSTOMER_PHOTO_DELETE_FAILED("31002"),

    // Theme errors (3101x)
    THEME_NOT_FOUND("31011"),
    THEME_INVALID_TEMPLATE_CONFIG("31012"),
    THEME_TEMPLATE_UPLOAD_FAILED("31013"),
    THEME_TEMPLATE_SYNC_FAILED("31014"),
    THEME_TEMPLATE_DOWNLOAD_FAILED("31015"),
    THEME_TEMPLATE_DELETE_FAILED("31016"),
    THEME_INVALID_FILE_NAME("31017"),
    THEME_INVALID_FILE_EXTENSION("31018"),
    THEME_INVALID_PATH("31019"),
    THEME_UNAUTHORIZED_ACCESS("31020"),

    // Team Management errors (311xx)
    TEAM_INVITATION_NOT_FOUND("31101"),
    TEAM_INVITATION_ALREADY_EXISTS("31102"),
    TEAM_INVITATION_INVALID("31103"),
    UNAUTHORIZED("31104"),

    //Customer errors (400xx)
    CUSTOMER_IDS_EMPTY("40001"),

    //Coupon errors (401xx)
    COUPON_IDS_EMPTY("40101");

    private final String errorCode;
}