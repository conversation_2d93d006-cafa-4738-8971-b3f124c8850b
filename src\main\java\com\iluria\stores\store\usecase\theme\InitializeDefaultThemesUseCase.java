package com.iluria.stores.store.usecase.theme;

import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.gateway.ThemeGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class InitializeDefaultThemesUseCase {

    private final ThemeGateway themeGateway;
    private final ThemeTemplateManagerUseCase themeTemplateManagerUseCase;
    private final UpdateThemePreviewUseCase updateThemePreviewUseCase;

    @Transactional
    public List<Theme> execute(ULID storeId) {
        if (storeId == null) {
            throw new IllegalArgumentException("Store ID cannot be null");
        }

        List<Theme> globalDefaultThemes = themeGateway.findDefaultThemes();
        if (globalDefaultThemes.isEmpty()) {
            throw new RuntimeException("No global default themes found. Please run database migrations.");
        }

        // Verificar quais temas padrão já foram copiados para esta loja
        List<Theme> existingStoreThemes = themeGateway.findStoreSpecificThemes(storeId);
        List<String> existingThemeNames = existingStoreThemes.stream()
                .map(Theme::getName)
                .collect(java.util.stream.Collectors.toList());

        List<Theme> copiedThemes = new ArrayList<>();

        for (Theme globalTheme : globalDefaultThemes) {
            // Só copiar se ainda não existe um tema com o mesmo nome na loja
            if (!existingThemeNames.contains(globalTheme.getName())) {
                Theme copiedTheme = copyThemeForStore(globalTheme, storeId);
                Theme savedTheme = themeGateway.save(copiedTheme);

                // Copiar templates S3 se existirem
                if (globalTheme.getHasTemplateFiles() && globalTheme.getTemplateFiles() != null && !globalTheme.getTemplateFiles().isEmpty()) {
                    copyTemplateFiles(globalTheme, savedTheme);
                    // Salvar novamente com as informações de template atualizadas
                    savedTheme = themeGateway.save(savedTheme);
                    
                    // Gerar preview para o novo tema após copiar os templates
                    generatePreviewForNewTheme(savedTheme, storeId);
                }

                copiedThemes.add(savedTheme);
            }
        }

        // Retornar todos os temas da loja (existentes + novos copiados)
        if (!copiedThemes.isEmpty()) {
            return themeGateway.findStoreSpecificThemes(storeId);
        }

        return existingStoreThemes;
    }

    /**
     * Força a inicialização de temas padrão, mesmo se já existirem temas na loja
     * Usado para garantir que todas as lojas tenham os temas padrão mais recentes
     */
    @Transactional
    public List<Theme> forceInitialize(ULID storeId) {
        if (storeId == null) {
            throw new IllegalArgumentException("Store ID cannot be null");
        }

        List<Theme> globalDefaultThemes = themeGateway.findDefaultThemes();
        if (globalDefaultThemes.isEmpty()) {
            throw new RuntimeException("No global default themes found. Please run database migrations.");
        }

        // Verificar quais temas padrão já foram copiados para esta loja
        List<Theme> existingStoreThemes = themeGateway.findStoreSpecificThemes(storeId);
        List<String> existingThemeNames = existingStoreThemes.stream()
                .map(Theme::getName)
                .collect(java.util.stream.Collectors.toList());

        List<Theme> copiedThemes = new ArrayList<>();

        for (Theme globalTheme : globalDefaultThemes) {
            // Sempre copiar, mesmo se já existe (para atualizações)
            if (!existingThemeNames.contains(globalTheme.getName())) {
                Theme copiedTheme = copyThemeForStore(globalTheme, storeId);
                Theme savedTheme = themeGateway.save(copiedTheme);

                // Copiar templates S3 se existirem
                if (globalTheme.getHasTemplateFiles() && globalTheme.getTemplateFiles() != null && !globalTheme.getTemplateFiles().isEmpty()) {
                    copyTemplateFiles(globalTheme, savedTheme);
                    // Salvar novamente com as informações de template atualizadas
                    savedTheme = themeGateway.save(savedTheme);
                    
                    // Gerar preview para o novo tema após copiar os templates
                    generatePreviewForNewTheme(savedTheme, storeId);
                }

                copiedThemes.add(savedTheme);
            }
        }

        return themeGateway.findStoreSpecificThemes(storeId);
    }

    private Theme copyThemeForStore(Theme globalTheme, ULID storeId) {
        ULID newThemeId = ULID.random(); // Novo ID para a cópia
        return Theme.builder()
                .id(newThemeId)
                .storeId(storeId)
                .name(globalTheme.getName())
                .description(globalTheme.getDescription())
                .categoryId(globalTheme.getCategoryId())
                .version(globalTheme.getVersion())
                .author(globalTheme.getAuthor())
                .isPremium(globalTheme.getIsPremium())
                .isDefault(false)
                .isActive(false)
                .cssVariables(globalTheme.getCssVariables())
                .typography(globalTheme.getTypography())
                .spacing(globalTheme.getSpacing())
                .customizations(globalTheme.getCustomizations())
                .previewImageUrl(globalTheme.getPreviewImageUrl())
                // Gerar path S3 único para evitar sobrescrever templates padrão
                .templateS3Path("stores/" + storeId + "/themes/" + newThemeId + "/templates/")
                .templateFiles(globalTheme.getTemplateFiles())
                .templateVersion(globalTheme.getTemplateVersion())
                .templateChecksum(globalTheme.getTemplateChecksum())
                .hasTemplateFiles(globalTheme.getHasTemplateFiles())
                .templateUpdatedAt(globalTheme.getTemplateUpdatedAt())
                // Gerar path S3 único para o preview
                .previewS3Path("stores/" + storeId + "/themes/" + newThemeId + "/preview/index.html")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * Copia os arquivos de template do tema padrão para o path único do tema copiado.
     * Usa uploadTemplateFilesForInitialization para NÃO criar arquivos no file-manager.
     */
    private void copyTemplateFiles(Theme sourceTheme, Theme destTheme) {
        try {
            // Tentar baixar os templates do tema padrão
            Map<String, String> templateFiles = null;
            
            try {
                templateFiles = themeTemplateManagerUseCase.downloadTemplateFiles(sourceTheme.getId());
            } catch (Exception e) {
                templateFiles = getDefaultTemplateFiles();
            }

            if (templateFiles != null && !templateFiles.isEmpty()) {
                // Upload dos templates para o novo path SEM copiar para file-manager
                themeTemplateManagerUseCase.uploadTemplateFilesForInitialization(
                    destTheme.getId(),
                    templateFiles,
                    sourceTheme.getTemplateVersion()
                );
            }
        } catch (Exception e) {
            // Se falhar ao copiar templates, não falha a operação toda
            // O ApplyThemeTemplateUseCase tentará baixar depois
            // Ignore template copy errors
        }
    }

    /**
     * Retorna templates HTML básicos embedded como fallback
     */
    private Map<String, String> getDefaultTemplateFiles() {
        Map<String, String> templates = new HashMap<>();
        
        // Template básico index.html
        templates.put("index.html", """
            <!DOCTYPE html>
            <html lang="pt-BR">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{{store.name}}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .header { text-align: center; margin-bottom: 40px; }
                    .products { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
                    .product { border: 1px solid #ddd; padding: 15px; text-align: center; }
                </style>
            </head>
            <body>
                <div class="container">
                    <header class="header">
                        <h1>{{store.name}}</h1>
                        <p>Bem-vindo à nossa loja!</p>
                    </header>
                    <main class="products">
                        <!-- Produtos serão inseridos aqui -->
                    </main>
                </div>
            </body>
            </html>
        """);
        
        // Template básico product.html
        templates.put("product.html", """
            <!DOCTYPE html>
            <html lang="pt-BR">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{{product.name}} - {{store.name}}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .product-detail { display: flex; gap: 20px; }
                    .product-image { flex: 1; }
                    .product-info { flex: 1; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="product-detail">
                        <div class="product-image">
                            <img src="{{product.image}}" alt="{{product.name}}" style="width: 100%;">
                        </div>
                        <div class="product-info">
                            <h1>{{product.name}}</h1>
                            <p>{{product.description}}</p>
                            <p><strong>Preço: {{product.price}}</strong></p>
                            <button>Adicionar ao Carrinho</button>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        """);
        
        // Template básico collection.html
        templates.put("collection.html", """
            <!DOCTYPE html>
            <html lang="pt-BR">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{{collection.name}} - {{store.name}}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .products { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
                    .product { border: 1px solid #ddd; padding: 15px; text-align: center; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>{{collection.name}}</h1>
                    <div class="products">
                        <!-- Produtos da coleção serão inseridos aqui -->
                    </div>
                </div>
            </body>
            </html>
        """);
        
        // Template básico cart.html
        templates.put("cart.html", """
            <!DOCTYPE html>
            <html lang="pt-BR">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Carrinho - {{store.name}}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .cart-item { border-bottom: 1px solid #ddd; padding: 15px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Seu Carrinho</h1>
                    <div class="cart-items">
                        <!-- Itens do carrinho serão inseridos aqui -->
                    </div>
                    <div class="cart-total">
                        <strong>Total: {{cart.total}}</strong>
                    </div>
                </div>
            </body>
            </html>
        """);
        
        // Template básico 404.html
        templates.put("404.html", """
            <!DOCTYPE html>
            <html lang="pt-BR">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Página não encontrada - {{store.name}}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; text-align: center; }
                    .container { max-width: 600px; margin: 0 auto; margin-top: 100px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>404 - Página não encontrada</h1>
                    <p>A página que você procura não existe.</p>
                    <a href="/">Voltar à página inicial</a>
                </div>
            </body>
            </html>
        """);
        
        // Adicionar mais templates conforme necessário
        templates.put("product-basic.html", templates.get("product.html"));
        templates.put("product-premium.html", templates.get("product.html"));
        templates.put("page-about.html", """
            <!DOCTYPE html>
            <html lang="pt-BR">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Sobre - {{store.name}}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    .container { max-width: 800px; margin: 0 auto; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Sobre {{store.name}}</h1>
                    <p>Esta é a página sobre nossa loja.</p>
                </div>
            </body>
            </html>
        """);
        
        return templates;
    }

    /**
     * Gera preview para o novo tema criado baseado no index.html dos templates
     */
    private void generatePreviewForNewTheme(Theme theme, ULID storeId) {
        try {
            // Baixar os templates do tema para obter o index.html
            Map<String, String> templateFiles = themeTemplateManagerUseCase.downloadTemplateFiles(theme.getId());
            
            if (templateFiles != null && templateFiles.containsKey("index.html")) {
                String indexHtmlContent = templateFiles.get("index.html");
                
                // Usar o UpdateThemePreviewUseCase para gerar o preview
                updateThemePreviewUseCase.execute(theme.getId(), storeId, indexHtmlContent);
                
            }
        } catch (Exception e) {
            // Se falhar ao gerar preview, não falha a operação toda
            // Ignore preview generation errors
        }
    }
}
