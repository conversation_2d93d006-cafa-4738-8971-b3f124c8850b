package com.iluria.stores.store.service;

import org.springframework.stereotype.Service;

import com.iluria.stores.websocket.ThemeProgressWebSocketHandler;
import com.iluria.stores.websocket.ThemeProgressWebSocketHandler.ThemeProgressMessage;

import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ThemeProgressService {

    private final ThemeProgressWebSocketHandler webSocketHandler;

    /**
     * Envia notificação de início da mudança de tema
     */
    public void sendThemeChangeStarted(ULID storeId, String themeName) {
        sendProgress(storeId, "THEME_CHANGE_STARTED", 
                    "Iniciando aplicação do tema " + themeName + "...", 5);
    }

    /**
     * Envia notificação de criação de backup
     */
    public void sendBackupCreating(ULID storeId) {
        sendProgress(storeId, "BACKUP_CREATING", 
                    "Criando backup do tema atual...", 20);
    }

    /**
     * Envia notificação de backup concluído
     */
    public void sendBackupCompleted(ULID storeId) {
        sendProgress(storeId, "BACKUP_COMPLETED", 
                    "Backup criado com sucesso", 35);
    }

    /**
     * Envia notificação de download de templates
     */
    public void sendTemplatesDownloading(ULID storeId) {
        sendProgress(storeId, "TEMPLATES_DOWNLOADING", 
                    "Baixando novos templates...", 50);
    }

    /**
     * Envia notificação de aplicação de templates
     */
    public void sendTemplatesApplying(ULID storeId) {
        sendProgress(storeId, "TEMPLATES_APPLYING", 
                    "Aplicando novo tema...", 70);
    }

    /**
     * Envia notificação de sincronização S3
     */
    public void sendS3Syncing(ULID storeId) {
        sendProgress(storeId, "S3_SYNCING", 
                    "Sincronizando arquivos com S3...", 85);
    }

    /**
     * Envia notificação de conclusão
     */
    public void sendThemeChangeCompleted(ULID storeId, String themeName) {
        sendProgress(storeId, "THEME_CHANGE_COMPLETED", 
                    "Tema \"" + themeName + "\" aplicado com sucesso!", 100);
    }

    /**
     * Envia notificação de erro
     */
    public void sendThemeChangeError(ULID storeId, String errorMessage) {
        sendProgress(storeId, "THEME_CHANGE_ERROR", 
                    "Erro: " + errorMessage, 0);
    }

    /**
     * Envia notificação de restauração de backup concluída
     */
    public void sendThemeRestoreCompleted(ULID storeId) {
        sendProgress(storeId, "THEME_RESTORE_COMPLETED", 
                    "Backup restaurado com sucesso!", 100);
    }

    /**
     * Método genérico para enviar progresso
     */
    private void sendProgress(ULID storeId, String type, String message, int progress) {
        try {
            ThemeProgressMessage progressMessage = new ThemeProgressMessage(type, message, progress);
            webSocketHandler.sendProgressUpdate(storeId, progressMessage);
        } catch (Exception e) {
        }
    }

    /**
     * Envia progresso customizado
     */
    public void sendCustomProgress(ULID storeId, String type, String message, int progress) {
        sendProgress(storeId, type, message, progress);
    }
}