package com.iluria.stores.store.usecase.theme;

import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeGateway;
import com.iluria.commons.domain.FileManager;
import com.iluria.commons.domain.FileManagerEnum;
import com.iluria.commons.gateway.FileManagerGateway;
import com.iluria.stores.layoutEditor.usecase.InitializeThemeLayoutsUseCase;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ApplyThemeTemplateUseCase {

    private final ThemeGateway themeGateway;
    private final CopyThemeTemplatesToFileManagerUseCase copyThemeTemplatesToFileManagerUseCase;
    private final SyncThemeTemplatesUseCase syncThemeTemplatesUseCase;
    private final ThemeDevEnvSyncUseCase themeDevEnvSyncUseCase;
    private final InitializeThemeLayoutsUseCase initializeThemeLayoutsUseCase;
    private final CleanupOrphanedFilesUseCase cleanupOrphanedFilesUseCase;
    private final FileManagerGateway fileManagerGateway;

    @Transactional
    public Theme execute(ULID themeId, ULID storeId) {
        // 🚫 PROTEÇÃO: Marcar loja como em processo de restore para evitar race conditions com DevEnvFilter
        com.iluria.stores.layoutEditor.filter.DevEnvFilter.markStoreInRestore(storeId.toString());
        
        try {
            // Find the theme
            Theme theme = themeGateway.findById(themeId);
            if (theme == null) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
            }

            if (theme.getStoreId() == null || !theme.getStoreId().equals(storeId)) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
            }

            if (!theme.hasValidTemplateConfiguration()) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_TEMPLATE_CONFIG);
            }

            // PRIMEIRO: Limpar arquivos de temas anteriores antes de aplicar o novo
            cleanupOrphanedFilesUseCase.executeFullCleanup(storeId, themeId, FileManagerEnum.DEVELOP);
            
            // Only attempt template operations if theme has valid template configuration
            if (theme.hasValidTemplateConfiguration()) {
                // First, synchronize theme templates to ensure they are up to date
                SyncThemeTemplatesUseCase.SyncOptions syncOptions = SyncThemeTemplatesUseCase.SyncOptions.builder()
                        .environment(FileManagerEnum.DEVELOP)
                        .forceUpdate(true)  // Force update to ensure fresh templates
                        .removeOrphaned(true)
                        .build();
                
                SyncThemeTemplatesUseCase.SyncResult syncResult = syncThemeTemplatesUseCase.execute(themeId, storeId, syncOptions);
                
                // If sync failed to create any files, try copying them directly or create basic templates
                if (syncResult.getAddedFiles().isEmpty() && syncResult.getUpdatedFiles().isEmpty()) {
                    CopyThemeTemplatesToFileManagerUseCase.CopyTemplateOptions copyOptions = 
                            CopyThemeTemplatesToFileManagerUseCase.CopyTemplateOptions.builder()
                                    .environment(FileManagerEnum.DEVELOP)
                                    .replaceExisting(true)
                                    .processVariables(true)
                                    .failOnError(false) // Don't fail if copy doesn't work
                                    .build();
                    
                    try {
                        copyThemeTemplatesToFileManagerUseCase.execute(themeId, storeId, copyOptions);
                    } catch (Exception e) {
                        // Clear template configuration since files don't exist
                        theme.markAsNotHavingTemplateFiles();
                        themeGateway.save(theme);
                    }
                }
            }
            
            // Activate the theme (deactivate others first)
            themeGateway.deactivateAllByStoreId(storeId);
            theme.activate();
            
            Theme savedTheme = themeGateway.save(theme);
            
            // Initialize layouts from theme templates if they exist
            try {
                initializeThemeLayoutsUseCase.syncLayoutsWithTheme(themeId, storeId);
            } catch (Exception e) {
            }
            
            // Validate that files were actually created in file-manager
            validateFilesInFileManager(storeId, theme);
            
            // Trigger DevEnvFilter synchronization after theme application
            themeDevEnvSyncUseCase.triggerSyncAfterThemeApplication(storeId, theme.getName());
            
            return savedTheme;
            
        } catch (Exception e) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED);
        } finally {
            // ✅ PROTEÇÃO: Sempre liberar flag de restore, mesmo em caso de erro
            com.iluria.stores.layoutEditor.filter.DevEnvFilter.markStoreRestoreCompleted(storeId.toString());
        }
    }

    @Transactional
    public List<FileManager> executeToEnvironment(ULID themeId, ULID storeId, FileManagerEnum environment) {
        // Find the theme
        Theme theme = themeGateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        if (theme.getStoreId() == null || !theme.getStoreId().equals(storeId)) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        if (!theme.hasValidTemplateConfiguration()) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_TEMPLATE_CONFIG);
        }
        
        try {
            // Copy template files to specified environment
            CopyThemeTemplatesToFileManagerUseCase.CopyTemplateOptions copyOptions = 
                    CopyThemeTemplatesToFileManagerUseCase.CopyTemplateOptions.builder()
                            .environment(environment)
                            .replaceExisting(true)
                            .processVariables(true)
                            .failOnError(false)
                            .build();
            
            List<FileManager> copiedFiles = copyThemeTemplatesToFileManagerUseCase.execute(themeId, storeId, copyOptions);
            
            // Initialize layouts for this environment if it's development
            if (environment == FileManagerEnum.DEVELOP) {
                initializeThemeLayoutsUseCase.syncLayoutsWithTheme(themeId, storeId);
            }
            
            return copiedFiles;
            
        } catch (Exception e) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED);
        }
    }

    /**
     * 🔍 NOVA FUNÇÃO: Valida se os arquivos do tema foram realmente criados no file-manager
     */
    private void validateFilesInFileManager(ULID storeId, Theme theme) {
        try {
            List<FileManager> filesInManager = fileManagerGateway.findByStoreIdAndEnvironment(storeId, FileManagerEnum.DEVELOP);
            
            // Verificar se pelo menos index.html existe
            boolean hasIndexHtml = filesInManager.stream()
                    .anyMatch(file -> "index.html".equals(file.getName()) && file.getType() == FileManagerEnum.ARCHIVE);
            
            if (!hasIndexHtml) {
                // Tentar forçar sincronização uma última vez
                if (theme.hasValidTemplateConfiguration()) {
                    try {
                        SyncThemeTemplatesUseCase.SyncOptions emergencyOptions = SyncThemeTemplatesUseCase.SyncOptions.builder()
                                .environment(FileManagerEnum.DEVELOP)
                                .forceUpdate(true)
                                .removeOrphaned(false) // Não remover arquivos existentes
                                .build();
                        
                        syncThemeTemplatesUseCase.execute(theme.getId(), storeId, emergencyOptions);
                    } catch (Exception e) {
                        // Ignore emergency sync errors
                    }
                }
            }
        } catch (Exception e) {
            // Não lançar exceção para não quebrar a aplicação do tema
        }
    }

}