package com.iluria.stores.store.usecase.theme;

import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeGateway;
import com.iluria.stores.store.service.ThemeLockService;
import com.iluria.stores.store.service.ThemeErrorHandler;
import com.iluria.exception.StoreErrorMessageEnum;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class ActivateThemeUseCase {

    private final ThemeGateway gateway;
    private final ThemeLockService themeLockService;
    private final ThemeErrorHandler themeErrorHandler;

    @Transactional
    public Theme execute(ULID themeId, ULID storeId) {
        // Acquire lock to prevent race conditions during theme activation
        if (!themeLockService.acquireThemeOperationLock(storeId)) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED);
        }
        
        try {
            Theme theme = gateway.findById(themeId);
            if (theme == null) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
            }

            if (theme.getStoreId() == null || !theme.getStoreId().equals(storeId)) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
            }

            if (!theme.isValidForActivation()) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_TEMPLATE_CONFIG);
            }

            gateway.deactivateAllByStoreId(storeId);

            theme.activate();
            Theme savedTheme = gateway.save(theme);
            
            return savedTheme;
        } catch (Exception e) {
            themeErrorHandler.handleError(storeId, "activate_theme", e);
            throw e;
        } finally {
            themeLockService.releaseThemeOperationLock(storeId);
        }
    }


}