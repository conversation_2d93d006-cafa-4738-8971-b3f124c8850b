package com.iluria.stores.store.domain;

import io.github.jaspeen.ulid.ULID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ThemeBackup {

    private ULID id;
    private ULID storeId;
    private ULID themeId;
    private String themeName;
    private String backupReason; // "THEME_CHANGE", "MANUAL_BACKUP", "SCHEDULED_BACKUP"
    private String backupS3Path;
    private String templateVersion;
    private String templateChecksum;
    private Integer fileCount;
    private Long backupSizeBytes;
    private LocalDateTime backedUpAt;
    private LocalDateTime expiresAt;
    
    @Builder.Default
    private Boolean isAutoBackup = false;
    
    @Builder.Default
    private Integer restorationCount = 0;
    
    private LocalDateTime lastRestoredAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Map<String, String> templateFiles;

    // Business logic methods
    
    public boolean hasValidTemplateContent() {
        // For S3-based backups, check if we have a valid S3 path and file count
        if (backupS3Path != null && !backupS3Path.trim().isEmpty() && fileCount != null && fileCount > 0) {
            return true;
        }
        // Fallback for legacy backups with templateFiles
        return templateFiles != null && !templateFiles.isEmpty();
    }
    
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    
    public boolean canBeRestored() {
        return !isExpired() && hasValidTemplateContent() && storeId != null && themeId != null;
    }
    
    public void markAsRestored() {
        this.lastRestoredAt = LocalDateTime.now();
        this.restorationCount = (this.restorationCount != null ? this.restorationCount : 0) + 1;
    }
    
    public boolean isManualBackup() {
        return !Boolean.TRUE.equals(isAutoBackup);
    }
    
    public long getTotalContentSize() {
        if (templateFiles == null || templateFiles.isEmpty()) {
            return 0L;
        }
        return templateFiles.values().stream()
            .mapToLong(content -> content.getBytes().length)
            .sum();
    }
    
    public int getTemplateFileCount() {
        return templateFiles != null ? templateFiles.size() : 0;
    }

    public String getBackupDescription() {
        StringBuilder description = new StringBuilder();
        description.append("Backup do tema '").append(themeName).append("'");

        if (backupReason != null) {
            switch (backupReason) {
                case "THEME_CHANGE":
                    description.append(" (criado automaticamente antes da troca de tema)");
                    break;
                case "MANUAL_BACKUP":
                    description.append(" (backup manual)");
                    break;
                case "SCHEDULED_BACKUP":
                    description.append(" (backup agendado)");
                    break;
                default:
                    description.append(" (").append(backupReason.toLowerCase()).append(")");
            }
        }

        if (fileCount != null && fileCount > 0) {
            description.append(" - ").append(fileCount).append(" arquivo");
            if (fileCount > 1) {
                description.append("s");
            }
        }

        if (backupSizeBytes != null && backupSizeBytes > 0) {
            description.append(" (").append(formatFileSize(backupSizeBytes)).append(")");
        }

        return description.toString();
    }

    public long calculateBackupSize() {
        if (backupSizeBytes != null) {
            return backupSizeBytes;
        }
        return getTotalContentSize();
    }

    public boolean isValid() {
        return !isExpired() &&
               hasValidTemplateContent() &&
               storeId != null &&
               themeId != null;
    }
    
    public String getValidationFailureReason() {
        if (isExpired()) {
            return "Backup expirado em " + expiresAt;
        }
        if (!hasValidTemplateContent()) {
            return "Backup não possui conteúdo válido (S3 path: " + backupS3Path + ", fileCount: " + fileCount + ")";
        }
        if (storeId == null) {
            return "Store ID não definido";
        }
        if (themeId == null) {
            return "Theme ID não definido";
        }
        return "Backup válido";
    }

    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
