package com.iluria.stores.store.usecase.theme;

import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.domain.ThemeBackup;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeGateway;
import com.iluria.stores.store.service.ThemeBackupService;
import com.iluria.stores.store.service.ThemeLockService;
import com.iluria.stores.store.service.ThemeErrorHandler;
import com.iluria.commons.usecase.fileManager.CreateFileUseCase;
import com.iluria.commons.usecase.fileManager.GetFileTreeUseCase;
import com.iluria.commons.domain.FileManager;
import com.iluria.commons.domain.FileManagerEnum;
import com.iluria.commons.gateway.FileManagerS3Gateway;
import com.iluria.commons.gateway.FileManagerGateway;

import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;


@Service
@RequiredArgsConstructor
public class SwitchThemeUseCase {

    private final ThemeGateway themeGateway;
    private final ApplyThemeTemplateUseCase applyThemeTemplateUseCase;

    private final GetFileTreeUseCase getFileTreeUseCase;
    private final CreateFileUseCase createFileUseCase;
    private final FileManagerS3Gateway fileManagerS3Gateway;
    private final FileManagerGateway fileManagerGateway;

    private final ThemeDevEnvSyncUseCase themeDevEnvSyncUseCase;
    private final ThemeBackupService themeBackupService;
    private final SyncThemeTemplatesUseCase syncThemeTemplatesUseCase;
    private final SyncBackupToFileManagerUseCase syncBackupToFileManagerUseCase;
    private final CleanupOrphanedFilesUseCase cleanupOrphanedFilesUseCase;
    private final ThemeLockService themeLockService;
    private final ThemeErrorHandler themeErrorHandler;

    @Transactional
    public Theme execute(ULID currentThemeId, ULID newThemeId, ULID storeId) {
        // Acquire lock to prevent race conditions
        if (!themeLockService.acquireThemeOperationLock(storeId)) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED);
        }
        
        // 🚫 PROTEÇÃO: Marcar loja como em processo de restore no INÍCIO para evitar race conditions com DevEnvFilter
        com.iluria.stores.layoutEditor.filter.DevEnvFilter.markStoreInRestore(storeId.toString());
        
        try {
            // Validate themes
            Theme currentTheme = themeGateway.findById(currentThemeId);
            Theme newTheme = themeGateway.findById(newThemeId);
            
            if (currentTheme == null) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
            }
            
            if (newTheme == null) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
            }
            
            if (!currentTheme.getStoreId().equals(storeId) || !newTheme.getStoreId().equals(storeId)) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
            }
            
            if (!newTheme.hasValidTemplateConfiguration()) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_TEMPLATE_CONFIG);
            }
            
            // 1. Check if there's an existing backup of the target theme to restore
            Optional<ThemeBackup> existingBackup = themeBackupService.findLatestBackup(storeId, newThemeId);
            
            Theme switchedTheme;
            
            if (existingBackup.isPresent() && existingBackup.get().isValid()) {
                // Restore from existing backup (preserving previous customizations)
                boolean restoreSuccess = themeBackupService.restoreBackup(storeId, existingBackup.get().getId());
                
                if (restoreSuccess) {
                    switchedTheme = themeGateway.findById(newThemeId);
                    
                    // Aguardar um pouco para garantir que operação anterior termine
                    try {
                        Thread.sleep(1000); // 1 segundo
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    
                    boolean restoreValidationSuccess = validateBackupSyncToFileManager(storeId, 8); // Esperamos ~8 arquivos HTML
                    
                    if (!restoreValidationSuccess) {
                        // PRIMEIRO: Limpar TODOS os arquivos HTML existentes para evitar conflitos
                        int deletedFiles = cleanupOrphanedFilesUseCase.clearAllHtmlFiles(storeId, FileManagerEnum.DEVELOP);
                        
                        // SEGUNDO: Usar nosso método como fallback
                        SyncBackupToFileManagerUseCase.SyncResult syncResult = 
                                syncBackupToFileManagerUseCase.executeLatestBackup(newThemeId, storeId);
                        
                        if (!syncResult.isSuccess()) {
                            forceCompleteFileSync(storeId, newThemeId);
                        }
                    }
                } else {
                    // Fallback to clean template if restore fails
                    switchedTheme = applyThemeTemplateUseCase.execute(newThemeId, storeId);
                }
            } else {
                // PRIMEIRO: Limpar TODOS os arquivos HTML existentes para template limpo
                int deletedFiles = cleanupOrphanedFilesUseCase.clearAllHtmlFiles(storeId, FileManagerEnum.DEVELOP);
                
                // SEGUNDO: Aplicar template limpo
                switchedTheme = applyThemeTemplateUseCase.execute(newThemeId, storeId);
            }
            
            // 2. Force complete file synchronization to ensure files are in file-manager
            forceCompleteFileSync(storeId, newThemeId);
            
            // 3. Limpar cache local do DevEnvFilter
            forceCleanDevEnvCache(storeId);

            // 4. Trigger DevEnvFilter synchronization after theme switch
            themeDevEnvSyncUseCase.triggerSyncAfterThemeSwitch(storeId, currentTheme.getName(), newTheme.getName());
            
            return switchedTheme;
            
        } catch (Exception e) {
            themeErrorHandler.handleError(storeId, "switch_theme", e);
            throw new ThemeException(themeErrorHandler.getErrorMessage("switch_theme", e));
        } finally {
            // ✅ PROTEÇÃO: SEMPRE liberar flag de restore e lock, mesmo em caso de erro
            com.iluria.stores.layoutEditor.filter.DevEnvFilter.markStoreRestoreCompleted(storeId.toString());
            themeLockService.releaseThemeOperationLock(storeId);
        }
    }
    
    
    /**
     * Força limpeza do cache local do DevEnvFilter para garantir que serve versão atualizada
     */
    private void forceCleanDevEnvCache(ULID storeId) {
        try {
            java.io.File devEnvDir = new java.io.File("/iluria/dev-env/" + storeId.toString());
            
            if (devEnvDir.exists() && devEnvDir.isDirectory()) {
                // Listar arquivos HTML atuais
                java.io.File[] htmlFiles = devEnvDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".html"));
                
                if (htmlFiles != null && htmlFiles.length > 0) {
                    for (java.io.File htmlFile : htmlFiles) {
                        htmlFile.delete();
                    }
                }
            }
            
        } catch (Exception e) {
            // Não propagar erro pois não é crítico
        }
    }

    private String downloadFileContent(FileManager file) throws IOException {
        try {
            byte[] contentBytes = fileManagerS3Gateway.getFileContent(file);
            if (contentBytes != null && contentBytes.length > 0) {
                return new String(contentBytes, StandardCharsets.UTF_8);
            }
            return "";
        } catch (Exception e) {
            return "";
        }
    }
    
    private void updateFileContent(ULID storeId, String fileName, String content) {
        try {
            createFileUseCase.execute(
                fileName, 
                content, 
                null, // parentFolderId
                FileManagerEnum.DEVELOP.name(), 
                storeId
            );
            
        } catch (Exception e) {
            
            throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_UPLOAD_FAILED);
        }
    }

    /**
     * 🚀 NOVA FUNÇÃO: Força sincronização completa dos arquivos do tema para garantir
     * que estejam disponíveis no file-manager antes do DevEnvFilter tentar servi-los
     */
    private void forceCompleteFileSync(ULID storeId, ULID themeId) {
        try {
            // Verificar se arquivos do tema existem no file-manager
            List<FileManager> currentFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeId, FileManagerEnum.DEVELOP);
            
            // Se não há arquivos suficientes, forçar sincronização
            boolean needsSync = currentFiles.isEmpty() || 
                               currentFiles.stream().noneMatch(f -> "index.html".equals(f.getName()));
            
            if (needsSync) {
                
                // Tentar sincronização via SyncThemeTemplatesUseCase
                try {
                    SyncThemeTemplatesUseCase.SyncOptions syncOptions = SyncThemeTemplatesUseCase.SyncOptions.builder()
                            .environment(FileManagerEnum.DEVELOP)
                            .forceUpdate(true)
                            .removeOrphaned(true)
                            .build();
                    
                    SyncThemeTemplatesUseCase.SyncResult syncResult = syncThemeTemplatesUseCase.execute(themeId, storeId, syncOptions);
                    
                    if (!syncResult.getAddedFiles().isEmpty() || !syncResult.getUpdatedFiles().isEmpty()) {
                        return;
                    }
                } catch (Exception e) {
                    themeErrorHandler.handleError(storeId, "sync_template", e);
                    // Falha na sincronização via SyncThemeTemplates
                }
                
                // Se chegou aqui, a sincronização ainda não funcionou
            } else {
                // Arquivos já existem no file-manager, não é necessário sincronizar
            }
            
        } catch (Exception e) {
            themeErrorHandler.handleError(storeId, "force_sync", e);
            // Não lançar exceção para não quebrar o processo de troca de tema
        }
    }

    /**
     * 🔍 NOVA FUNÇÃO: Valida se a sincronização backup→file-manager realmente funcionou
     */
    private boolean validateBackupSyncToFileManager(ULID storeId, int expectedFileCount) {
        try {
            
            // Contar arquivos HTML no file-manager
            List<FileManager> filesInManager = fileManagerGateway.findByStoreIdAndEnvironment(storeId, FileManagerEnum.DEVELOP);
            
            long htmlFileCount = filesInManager.stream()
                    .filter(f -> f.getType() == FileManagerEnum.ARCHIVE)
                    .filter(f -> f.getName() != null && f.getName().toLowerCase().endsWith(".html"))
                    .count();
            
            
            // Verificar se tem pelo menos index.html
            boolean hasIndexHtml = filesInManager.stream()
                    .anyMatch(f -> "index.html".equals(f.getName()) && f.getType() == FileManagerEnum.ARCHIVE);
            
            if (!hasIndexHtml) {
                return false;
            }
            
            // Verificar se número de arquivos é razoável (pelo menos 1)
            if (htmlFileCount < 1) {
                return false;
            }
            
            // Se esperamos um número específico, verificar se temos pelo menos 50% dos arquivos
            if (expectedFileCount > 1 && htmlFileCount < (expectedFileCount * 0.5)) {
                // Não falhar se index.html está presente, apenas avisar
            }
            
            // Se temos arquivos e index.html está presente, consideramos sucesso
            return true;
            
        } catch (Exception e) {
            themeErrorHandler.handleError(storeId, "validate_sync", e);
            return false;
        }
    }

    public static class SwitchThemeRequest {
        private ULID currentThemeId;
        private ULID newThemeId;
        private ULID storeId;
        
        public SwitchThemeRequest(ULID currentThemeId, ULID newThemeId, ULID storeId) {
            this.currentThemeId = currentThemeId;
            this.newThemeId = newThemeId;
            this.storeId = storeId;
        }
        
        public ULID getCurrentThemeId() {
            return currentThemeId;
        }
        
        public ULID getNewThemeId() {
            return newThemeId;
        }
        
        public ULID getStoreId() {
            return storeId;
        }
    }
}