package com.iluria.stores.store.usecase.theme;

import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.gateway.ThemeGateway;

@Service
@RequiredArgsConstructor
public class GetActiveThemeUseCase {

    private final ThemeGateway gateway;

    public Theme execute(ULID storeId) {
        // Always load from database
        return gateway.findActiveByStoreId(storeId);
    }
}