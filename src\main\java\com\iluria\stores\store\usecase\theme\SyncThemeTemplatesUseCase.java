package com.iluria.stores.store.usecase.theme;

import com.iluria.commons.domain.FileManager;
import com.iluria.commons.domain.FileManagerEnum;
import com.iluria.commons.gateway.FileManagerGateway;
import com.iluria.commons.gateway.StoreGateway;
import com.iluria.commons.usecase.fileManager.RegisterFileManagerChangeUseCase;
import com.iluria.stores.store.domain.Theme;
import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import org.springframework.beans.factory.annotation.Value;


import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;

import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class SyncThemeTemplatesUseCase {

    private final ThemeGateway themeGateway;
    private final FileManagerGateway fileManagerGateway;
    private final StoreGateway storeGateway;
    private final CreateTemplateFileUseCase createTemplateFileUseCase;
    private final RegisterFileManagerChangeUseCase registerFileManagerChangeUseCase;
    private final S3Client s3Client;

    @Value("${aws.s3.bucket-name}")
    private String bucketName;


    @Transactional
    public SyncResult execute(ULID themeId, ULID storeId, SyncOptions options) {
        // Validate that the store exists first - this prevents all subsequent foreign key errors
        if (storeId == null) {
            throw new ThemeException(StoreErrorMessageEnum.STORE_NOT_FOUND);
        }
        
        if (storeGateway.findById(storeId).isEmpty()) {
            throw new ThemeException(StoreErrorMessageEnum.STORE_NOT_FOUND);
        }
        
        // Find and validate the theme
        Theme theme = themeGateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        if (theme.getStoreId() == null || !theme.getStoreId().equals(storeId)) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        if (!theme.hasValidTemplateConfiguration()) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_TEMPLATE_CONFIG);
        }
        
        try {
            SyncResult result = synchronizeTemplateFiles(theme, storeId, options);
            
            // Update theme's template information
            updateThemeTemplateInfo(theme, result);
            
            // Register file manager change to trigger DevEnvFilter sync
            registerFileManagerChangeUseCase.execute(storeId, options.getEnvironment());
            
            
            return result;
            
        } catch (org.springframework.dao.DataIntegrityViolationException e) {
            if (e.getMessage().contains("fk_file_manager_store") || e.getMessage().contains("fk_env_manager_audit_store")) {
                throw new ThemeException(StoreErrorMessageEnum.STORE_NOT_FOUND);
            } else {
                throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED);
            }
        } catch (ThemeException e) {
            // Re-throw ThemeExceptions without wrapping
            throw e;
        } catch (Exception e) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED);
        }
    }


    public boolean isSyncNeeded(ULID themeId, ULID storeId, FileManagerEnum environment) {
        try {
            Theme theme = themeGateway.findById(themeId);
            if (theme == null || !theme.hasValidTemplateConfiguration()) {
                return false;
            }
            
            // Get current template files from FileManager
            List<FileManager> currentFiles = fileManagerGateway.findByStoreIdAndTemplateId(storeId, themeId)
                    .stream()
                    .filter(file -> file.getEnvironment() == environment)
                    .toList();
            
            // Check if number of files matches
            Set<String> themeTemplateFiles = theme.getTemplateFiles();
            if (themeTemplateFiles.size() != currentFiles.size()) {
                return true;
            }
            
            // Check if all theme template files exist in FileManager
            Map<String, FileManager> currentFileMap = new HashMap<>();
            currentFiles.forEach(file -> currentFileMap.put(file.getName(), file));
            
            for (String templateFile : themeTemplateFiles) {
                FileManager currentFile = currentFileMap.get(templateFile);
                if (currentFile == null) {
                    return true; // File missing in FileManager
                }
                
                // Check if file content has changed (basic check using updateAt vs templateUpdatedAt)
                if (theme.getTemplateUpdatedAt() != null && currentFile.getUpdatedAt() != null) {
                    if (theme.getTemplateUpdatedAt().isAfter(currentFile.getUpdatedAt())) {
                        return true; // Template is newer than current file
                    }
                }
            }
            
            return false;
            
        } catch (Exception e) {
            return true; // Assume sync is needed if we can't determine
        }
    }

    @Transactional
    public SyncResult forceFullSync(ULID themeId, ULID storeId, FileManagerEnum environment) {
        SyncOptions options = SyncOptions.builder()
                .environment(environment)
                .forceUpdate(true)
                .removeOrphaned(true)
                .validateChecksums(false) // Skip validation for force sync
                .build();
        
        return execute(themeId, storeId, options);
    }

    private SyncResult synchronizeTemplateFiles(Theme theme, ULID storeId, SyncOptions options) throws IOException {
        SyncResult result = new SyncResult();
        
        Set<String> themeTemplateFiles = theme.getTemplateFiles();
        if (themeTemplateFiles == null || themeTemplateFiles.isEmpty()) {
            return result;
        }
        
        
        
        // Get current template files from FileManager - this will also validate store exists
        List<FileManager> currentFiles;
        try {
            currentFiles = fileManagerGateway.findByStoreIdAndTemplateId(storeId, theme.getId())
                    .stream()
                    .filter(file -> file.getEnvironment() == options.getEnvironment())
                    .toList();
        } catch (Exception e) {
            throw new ThemeException(StoreErrorMessageEnum.STORE_NOT_FOUND);
        }
        
        Map<String, FileManager> currentFileMap = new HashMap<>();
        currentFiles.forEach(file -> currentFileMap.put(file.getName(), file));
        
        // Process each template file
        for (String templateFile : themeTemplateFiles) {
            try {
                FileManager currentFile = currentFileMap.get(templateFile);
                SyncAction action = determineSyncAction(theme, templateFile, currentFile, options);
                
                switch (action) {
                    case ADD:
                        FileManager addedFile = addTemplateFile(theme, storeId, templateFile, options);
                        result.getAddedFiles().add(addedFile);
                        break;
                        
                    case UPDATE:
                        FileManager updatedFile = updateTemplateFile(theme, storeId, templateFile, currentFile, options);
                        result.getUpdatedFiles().add(updatedFile);
                        break;
                        
                    case SKIP:
                        result.getSkippedFiles().add(currentFile);
                        break;
                }
                
            } catch (Exception e) {
                result.getErrors().add("Failed to sync " + templateFile + ": " + e.getMessage());
            }
        }
        
        // Remove orphaned files if requested
        if (options.isRemoveOrphaned()) {
            List<FileManager> orphanedFiles = findOrphanedFiles(currentFiles, themeTemplateFiles);
            for (FileManager orphanedFile : orphanedFiles) {
                try {
                    fileManagerGateway.deleteById(orphanedFile.getId());
                    result.getRemovedFiles().add(orphanedFile);
                } catch (Exception e) {
                    result.getErrors().add("Failed to remove orphaned file " + orphanedFile.getName() + ": " + e.getMessage());
                }
            }
        }
        
        return result;
    }

    private SyncAction determineSyncAction(Theme theme, String templateFile, FileManager currentFile, SyncOptions options) {
        if (currentFile == null) {
            return SyncAction.ADD;
        }
        
        if (options.isForceUpdate()) {
            return SyncAction.UPDATE;
        }
        
        // Check if file needs updating based on modification time
        if (theme.getTemplateUpdatedAt() != null && currentFile.getUpdatedAt() != null) {
            if (theme.getTemplateUpdatedAt().isAfter(currentFile.getUpdatedAt())) {
                return SyncAction.UPDATE;
            }
        }
        
        // Validate checksums if requested
        if (options.isValidateChecksums()) {
            try {
                if (!validateFileChecksum(theme, templateFile, currentFile)) {
                    return SyncAction.UPDATE;
                }
            } catch (Exception e) {
                return SyncAction.UPDATE;
            }
        }
        
        return SyncAction.SKIP;
    }

    private boolean validateFileChecksum(Theme theme, String templateFile, FileManager currentFile) throws IOException {
        try {
            // Validate that file exists in S3
            String s3Key = theme.getTemplateFileS3Path(templateFile);

            // For now, just check if we can access the file
            downloadFileFromS3(s3Key); // Just to validate file exists

            return true;
            
        } catch (Exception e) {
            return false;
        }
    }

    private FileManager addTemplateFile(Theme theme, ULID storeId, String templateFile, SyncOptions options) throws IOException {
        // Validate that the store still exists before creating template file
        if (storeId == null) {
            throw new ThemeException(StoreErrorMessageEnum.STORE_NOT_FOUND);
        }
        
        // Double-check that theme's storeId matches the provided storeId
        if (!theme.getStoreId().equals(storeId)) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        String s3Key = theme.getTemplateFileS3Path(templateFile);
        
        String content = downloadFileFromS3(s3Key);
        
        CreateTemplateFileUseCase.CreateTemplateFileRequest request = CreateTemplateFileUseCase.CreateTemplateFileRequest.builder()
                .storeId(storeId)
                .fileName(templateFile)
                .content(content)
                .contentType(getContentType(templateFile))
                .environment(options.getEnvironment())
                .themeId(theme.getId())
                .templateFileId(null) // No parent template file relationship needed
                .build();
        
        try {
            FileManager createdFile = createTemplateFileUseCase.execute(request);

            return createdFile;
        } catch (org.springframework.dao.DataIntegrityViolationException e) {
            // Handle foreign key constraint failures specifically
            if (e.getMessage().contains("fk_file_manager_store")) {
                throw new ThemeException(StoreErrorMessageEnum.STORE_NOT_FOUND);
            }
            // Handle unique constraint violations for file_manager
            if (e.getMessage().contains("uk_file_manager_unique_file")) {
                // Try to find and return the existing file
                try {
                    var existingFile = fileManagerGateway.findByNameAndParentFolderIdAndEnvironmentAndStoreId(
                            request.getFileName(), null, request.getEnvironment(), request.getStoreId());

                    if (existingFile.isPresent()) {
                        // CORREÇÃO: Usar createTemplateFileUseCase para sobrescrever arquivo com novo conteúdo
                        try {
                            CreateTemplateFileUseCase.CreateTemplateFileRequest updateRequest = 
                                CreateTemplateFileUseCase.CreateTemplateFileRequest.builder()
                                    .storeId(request.getStoreId())
                                    .fileName(request.getFileName())
                                    .content(content)
                                    .environment(request.getEnvironment())
                                    .parentFolderId(null)
                                    .themeId(request.getThemeId())
                                    .templateFileId(request.getTemplateFileId())
                                    .build();
                            
                            FileManager updatedFile = createTemplateFileUseCase.execute(updateRequest);
                            return updatedFile;
                        } catch (Exception createError) {
                            throw new RuntimeException("Erro ao atualizar arquivo duplicado", createError);
                        }
                    }
                } catch (Exception findError) {
                    // Ignore error
                }
            }
            throw e; // Re-throw other data integrity violations
        }
    }

    private FileManager updateTemplateFile(Theme theme, ULID storeId, String templateFile, 
                                         FileManager currentFile, SyncOptions options) throws IOException {
        // Delete existing file
        fileManagerGateway.deleteById(currentFile.getId());
        
        // Create new file with updated content
        return addTemplateFile(theme, storeId, templateFile, options);
    }

    private List<FileManager> findOrphanedFiles(List<FileManager> currentFiles, Set<String> themeTemplateFiles) {
        return currentFiles.stream()
                .filter(file -> !themeTemplateFiles.contains(file.getName()))
                .toList();
    }

    private String downloadFileFromS3(String s3Key) throws IOException {
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(getBucketName())
                .key(s3Key)
                .build();
        
        try (ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest)) {
            String content = new String(s3Object.readAllBytes(), StandardCharsets.UTF_8);
            return content;
        } catch (Exception e) {
            throw new IOException("Failed to download file from S3: " + s3Key, e);
        }
    }

    private void updateThemeTemplateInfo(Theme theme, SyncResult result) {
        try {
            // Update template updated timestamp if any changes were made
            if (!result.getAddedFiles().isEmpty() || !result.getUpdatedFiles().isEmpty() || !result.getRemovedFiles().isEmpty()) {
                theme.updateTemplateChecksum(calculateThemeChecksum(theme));
                themeGateway.save(theme);
            }
        } catch (Exception e) {
        }
    }

    private String calculateThemeChecksum(Theme theme) {
        try {
            StringBuilder checksumInput = new StringBuilder();
            if (theme.getTemplateFiles() != null) {
                theme.getTemplateFiles().stream().sorted().forEach(checksumInput::append);
            }
            checksumInput.append(theme.getTemplateVersion());
            if (theme.getTemplateUpdatedAt() != null) {
                checksumInput.append(theme.getTemplateUpdatedAt().toString());
            }
            
            return calculateChecksum(checksumInput.toString());
        } catch (Exception e) {
            return null;
        }
    }

    private String calculateChecksum(String content) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(content.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    private String getContentType(String fileName) {
        if (fileName.endsWith(".html")) {
            return "text/html";
        } else if (fileName.endsWith(".css")) {
            return "text/css";
        } else if (fileName.endsWith(".js")) {
            return "application/javascript";
        } else if (fileName.endsWith(".json")) {
            return "application/json";
        } else {
            return "text/plain";
        }
    }

    private String getBucketName() {
        return bucketName;
    }

    private enum SyncAction {
        ADD, UPDATE, SKIP
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class SyncOptions {
        @lombok.Builder.Default
        private FileManagerEnum environment = FileManagerEnum.DEVELOP;
        
        @lombok.Builder.Default
        private boolean forceUpdate = false;
        
        @lombok.Builder.Default
        private boolean removeOrphaned = true;
        
        @lombok.Builder.Default
        private boolean validateChecksums = false;
    }

    @lombok.Data
    public static class SyncResult {
        private List<FileManager> addedFiles = new ArrayList<>();
        private List<FileManager> updatedFiles = new ArrayList<>();
        private List<FileManager> removedFiles = new ArrayList<>();
        private List<FileManager> skippedFiles = new ArrayList<>();
        private List<String> errors = new ArrayList<>();
        
        public boolean hasChanges() {
            return !addedFiles.isEmpty() || !updatedFiles.isEmpty() || !removedFiles.isEmpty();
        }
        
        public int getTotalChanges() {
            return addedFiles.size() + updatedFiles.size() + removedFiles.size();
        }
        
        public boolean hasErrors() {
            return !errors.isEmpty();
        }
    }
}