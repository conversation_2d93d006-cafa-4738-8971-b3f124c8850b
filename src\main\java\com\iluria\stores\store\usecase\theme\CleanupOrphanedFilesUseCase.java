package com.iluria.stores.store.usecase.theme;

import com.iluria.commons.domain.FileManager;
import com.iluria.commons.domain.FileManagerEnum;
import com.iluria.commons.gateway.FileManagerGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
@RequiredArgsConstructor
public class CleanupOrphanedFilesUseCase {

    private final FileManagerGateway fileManagerGateway;

    @Transactional
    public int removeOrphanedFiles(ULID storeId, FileManagerEnum environment) {
        try {
            // Buscar todos os arquivos no ambiente especificado
            List<FileManager> allFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeId, environment);
            
            // Filtrar apenas arquivos órfãos (sem templateId)
            List<FileManager> orphanedFiles = allFiles.stream()
                    .filter(file -> file.getTemplateId() == null)
                    .toList();
            
            if (orphanedFiles.isEmpty()) {
                return 0;
            }
            
            int removedCount = 0;
            for (FileManager file : orphanedFiles) {
                try {
                    fileManagerGateway.deleteById(file.getId());
                    removedCount++;
                } catch (Exception e) {
                    // Ignore delete errors
                }
            }
            
            return removedCount;
            
        } catch (Exception e) {
            return 0;
        }
    }

    @Transactional
    public int removeOldThemeFiles(ULID storeId, ULID currentThemeId, FileManagerEnum environment) {
        try {
            // Buscar todos os arquivos no ambiente especificado
            List<FileManager> allFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeId, environment);
            
            // Filtrar arquivos de outros temas (não o tema atual)
            List<FileManager> oldThemeFiles = allFiles.stream()
                    .filter(file -> file.getTemplateId() != null && !file.getTemplateId().equals(currentThemeId))
                    .toList();
            
            if (oldThemeFiles.isEmpty()) {
                return 0;
            }
            
            int removedCount = 0;
            for (FileManager file : oldThemeFiles) {
                try {
                    fileManagerGateway.deleteById(file.getId());
                    removedCount++;
                } catch (Exception e) {
                    // Ignore delete errors
                }
            }
            
            return removedCount;
            
        } catch (Exception e) {
            return 0;
        }
    }

    @Transactional
    public int executeFullCleanup(ULID storeId, ULID currentThemeId, FileManagerEnum environment) {
        int orphanedRemoved = removeOrphanedFiles(storeId, environment);
        int oldThemeRemoved = removeOldThemeFiles(storeId, currentThemeId, environment);
        
        int totalRemoved = orphanedRemoved + oldThemeRemoved;
        
        return totalRemoved;
    }


    public List<FileManager> previewCleanup(ULID storeId, ULID currentThemeId, FileManagerEnum environment) {
        try {
            List<FileManager> allFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeId, environment);
            
            List<FileManager> filesToRemove = allFiles.stream()
                    .filter(file -> {
                        // Arquivos órfãos (sem templateId)
                        if (file.getTemplateId() == null) {
                            return true;
                        }
                        // Arquivos de outros temas
                        return !file.getTemplateId().equals(currentThemeId);
                    })
                    .toList();
            
            return filesToRemove;
            
        } catch (Exception e) {
            return List.of();
        }
    }

    /**
     * 🧹 NOVA FUNÇÃO: Remove TODOS os arquivos HTML do file-manager para limpeza completa
     * (usado durante troca de tema para garantir que arquivos antigos não permaneçam)
     */
    @Transactional
    public int clearAllHtmlFiles(ULID storeId, FileManagerEnum environment) {
        try {
            // Buscar todos os arquivos no ambiente especificado
            List<FileManager> allFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeId, environment);
            
            // Filtrar apenas arquivos HTML
            List<FileManager> htmlFiles = allFiles.stream()
                    .filter(file -> file.getType() == FileManagerEnum.ARCHIVE)
                    .filter(file -> file.getName() != null && file.getName().toLowerCase().endsWith(".html"))
                    .toList();
            
            if (htmlFiles.isEmpty()) {
                return 0;
            }
            
            int removedCount = 0;
            for (FileManager file : htmlFiles) {
                try {
                    fileManagerGateway.deleteById(file.getId());
                    removedCount++;
                } catch (Exception e) {
                    // Ignore delete errors
                }
            }
            
            return removedCount;
            
        } catch (Exception e) {
            return 0;
        }
    }
}
