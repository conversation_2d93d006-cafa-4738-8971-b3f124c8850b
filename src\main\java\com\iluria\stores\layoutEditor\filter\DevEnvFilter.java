package com.iluria.stores.layoutEditor.filter;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.iluria.commons.domain.FileManagerEnum;
import com.iluria.commons.entity.EnvManagerAuditDBEntity;
import com.iluria.commons.entity.EnvVerificationDBEntity;
import com.iluria.commons.gateway.db.repository.EnvManagerAuditRepository;
import com.iluria.commons.gateway.db.repository.EnvManagerVerificationRepository;
import com.iluria.commons.service.AuthorizationContext;
import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.stores.layoutEditor.exception.DevEnvFilterException;
import com.iluria.commons.usecase.fileManager.DownloadRootFilesUseCase;
import com.iluria.commons.usecase.fileManager.CreateFileUseCase;
import com.iluria.commons.gateway.FileManagerGateway;
import com.iluria.commons.domain.FileManager;
import com.iluria.stores.store.gateway.ThemeGateway;
import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.usecase.theme.GetActiveThemeUseCase;

import io.github.jaspeen.ulid.ULID;
import jakarta.servlet.FilterChain;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
public class DevEnvFilter extends OncePerRequestFilter {

    private static final String DEV_ENV_ROOT_PATH = "/iluria";
    private static final String DEV_ENV_URI_PREFIX = "/dev-env";
    private static final String DEV_ENV_URI_SEPARATOR = "/";
    private static final String INDEX_HTML = "index.html";
    private static final String DEV_ENV_RESOURCE_PATH = DEV_ENV_ROOT_PATH + DEV_ENV_URI_PREFIX;
    private static final String S3_BASE_URL = "https://iluria-bucket-dev.s3.us-east-1.amazonaws.com";
    private static final String S3_HTML_PATH_TEMPLATE = "/file-manager/dev/%s";
    private static final long HTML_CACHE_TIMEOUT = 5 * 60 * 1000L;
    
    // 🚫 PROTEÇÃO: Flag para evitar sincronização durante operações de restore
    private static final java.util.Set<String> STORES_IN_RESTORE = java.util.concurrent.ConcurrentHashMap.newKeySet();

    private final EnvManagerAuditRepository auditRepository;
    private final EnvManagerVerificationRepository verificationRepository;
    private final DownloadRootFilesUseCase downloadRootFilesUseCase;
    private final FileManagerGateway fileManagerGateway;
    private final CreateFileUseCase createFileUseCase;
    private final com.iluria.commons.gateway.FileManagerS3Gateway fileManagerS3Gateway;
    private final GetActiveThemeUseCase getActiveThemeUseCase;
    private final HttpClient httpClient;

    public DevEnvFilter(EnvManagerAuditRepository auditRepository,
            EnvManagerVerificationRepository verificationRepository,
            DownloadRootFilesUseCase downloadRootFilesUseCase,
            FileManagerGateway fileManagerGateway,
            CreateFileUseCase createFileUseCase,
            com.iluria.commons.gateway.FileManagerS3Gateway fileManagerS3Gateway,
            GetActiveThemeUseCase getActiveThemeUseCase) {
        this.auditRepository = auditRepository;
        this.verificationRepository = verificationRepository;
        this.downloadRootFilesUseCase = downloadRootFilesUseCase;
        this.fileManagerGateway = fileManagerGateway;
        this.createFileUseCase = createFileUseCase;
        this.fileManagerS3Gateway = fileManagerS3Gateway;
        this.getActiveThemeUseCase = getActiveThemeUseCase;
        this.httpClient = HttpClient.newHttpClient();
    }
    
    /**
     * 🚫 MÉTODOS PÚBLICOS: Controle de estado de restore para evitar race conditions
     */
    public static void markStoreInRestore(String storeId) {
        STORES_IN_RESTORE.add(storeId);
    }
    
    public static void markStoreRestoreCompleted(String storeId) {
        STORES_IN_RESTORE.remove(storeId);
    }
    
    public static boolean isStoreInRestore(String storeId) {
        return STORES_IN_RESTORE.contains(storeId);
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain)
            throws ServletException, IOException {

        if (!isDevEnvRequest(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        String storeIdStr = AuthorizationContext.getStoreId();
        
        // Se não há storeId no contexto, tenta extrair do cabeçalho X-Store-Id ou da URL
        if (storeIdStr == null || storeIdStr.trim().isEmpty()) {
            // Tentar obter o storeId do cabeçalho X-Store-Id
            String headerStoreId = request.getHeader("X-Store-Id");
            if (headerStoreId != null && !headerStoreId.trim().isEmpty()) {
                storeIdStr = headerStoreId.trim();
            } else {
                // Tentar extrair storeId da URL /dev-env/{storeId}/...
                String uri = request.getRequestURI();
                String[] pathParts = uri.split("/");
                if (pathParts.length >= 3 && "dev-env".equals(pathParts[1])) {
                    String potentialStoreId = pathParts[2];
                    try {
                        // Validar se é um ULID válido
                        ULID.fromString(potentialStoreId);
                        storeIdStr = potentialStoreId;
                    } catch (IllegalArgumentException e) {
                        // Invalid ULID in path, storeIdStr remains null
                    }
                }
            }
            
            // Se ainda não encontrou o storeId, retorna erro 401
            if (storeIdStr == null || storeIdStr.trim().isEmpty()) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json");
                response.getWriter().write("{\"error\":\"Authentication required\",\"message\":\"StoreId not found in authorization context, X-Store-Id header, or URL path\"}");
                return;
            }
        }
        
        try {
            ULID storeUlid = ULID.fromString(storeIdStr);

            byte[] storeId = storeUlid.toBytes();
            FileManagerEnum environment = FileManagerEnum.DEVELOP;
            String ipAddress = InetAddress.getLocalHost().getHostAddress();

            final String storePath = DEV_ENV_URI_PREFIX + DEV_ENV_URI_SEPARATOR + storeUlid;
            File targetDir = new File(DEV_ENV_ROOT_PATH, storePath);

            boolean shouldSync = shouldSynchronizeFiles(storeId, environment, ipAddress);
            
            // 🚫 PROTEÇÃO: Não sincronizar se loja está em processo de restore
            if (isStoreInRestore(storeIdStr)) {
                shouldSync = false;
            }

            if (targetDir.exists()) {
                File[] files = targetDir.listFiles();
                if (files == null || files.length == 0) {
                    shouldSync = true;
                }
            } else {
                shouldSync = true;
            }

            if (shouldSync) {
                // 🚀 OTIMIZAÇÃO: Sincronização inteligente em vez de completa
                synchronizeFilesOptimized(storeId, storeUlid, environment, ipAddress);
            }
        } catch (Exception e) {
            // Continue sem lançar exception, pois pode ser problema temporário
        }

        refreshDevEnv(storeIdStr);

        forwardRequest(request, response, storeIdStr);
    }

    private void forwardRequest(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            String storeId) throws ServletException, IOException {
        String forwardPath = getForwardPath(request, storeId);

        boolean isIndexRequest = request.getRequestURI().endsWith("/index.html") || 
                                request.getRequestURI().equals(DEV_ENV_URI_PREFIX) || 
                                request.getRequestURI().equals(DEV_ENV_URI_PREFIX + "/");

        boolean isHtmlRequest = request.getRequestURI().endsWith(".html");

        if (isIndexRequest) {
            forwardPath = handleHtmlFile(request, storeId, INDEX_HTML);
        } else if (isHtmlRequest) {
            String fileName = extractFileNameFromPath(request.getRequestURI());
            forwardPath = handleHtmlFile(request, storeId, fileName);
        }

        RequestDispatcher dispatcher = request.getRequestDispatcher(forwardPath);
        dispatcher.forward(request, response);
    }

    private String extractFileNameFromPath(String requestURI) {
        String path = requestURI.substring(DEV_ENV_URI_PREFIX.length());
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        int lastSlashIndex = path.lastIndexOf('/');
        if (lastSlashIndex >= 0) {
            path = path.substring(lastSlashIndex + 1);
        }
        return path;
    }

    private String handleHtmlFile(HttpServletRequest request, String storeId, String fileName) 
            throws ServletException, IOException {
        ULID storeUlid = ULID.fromString(storeId);
        final String storePath = DEV_ENV_URI_PREFIX + DEV_ENV_URI_SEPARATOR + storeUlid;
        File targetDir = new File(DEV_ENV_ROOT_PATH, storePath);
        File htmlFile = new File(targetDir, fileName);

        if (htmlFile.exists()) {
            boolean forceUpdate = "true".equals(request.getParameter("forceUpdate")) ||
                                 "true".equals(request.getParameter("refresh"));
            
            if (!forceUpdate) {
                long fileAge = System.currentTimeMillis() - htmlFile.lastModified();
                boolean cacheExpired = fileAge > HTML_CACHE_TIMEOUT;
                
                if (!cacheExpired) {
                    return DEV_ENV_RESOURCE_PATH + DEV_ENV_URI_SEPARATOR + storeId + DEV_ENV_URI_SEPARATOR + fileName;
                }
            }
        }

        try {
            String s3PathTemplate = String.format(S3_HTML_PATH_TEMPLATE, fileName);
            
            if (!htmlFile.exists() || shouldUpdateHtmlFileFromS3(storeUlid, htmlFile, s3PathTemplate)) {
                if (htmlFile.exists()) {
                    htmlFile.delete();
                }
                downloadHtmlFileFromS3(storeUlid, targetDir, fileName, s3PathTemplate);
                
                syncIndividualHtmlFileWithDatabase(storeUlid, fileName, htmlFile);
            }
        } catch (Exception e) {
            try {
                synchronizeFiles(storeUlid.toBytes(), storeUlid, FileManagerEnum.DEVELOP, InetAddress.getLocalHost().getHostAddress());
            } catch (Exception syncException) {
                if (!htmlFile.exists()) {
                    throw new DevEnvFilterException(StoreErrorMessageEnum.FAILED_TO_SYNCHRONIZE_FILES);
                }
            }
        }

        return DEV_ENV_RESOURCE_PATH + DEV_ENV_URI_SEPARATOR + storeId + DEV_ENV_URI_SEPARATOR + fileName;
    }

    private boolean isDevEnvRequest(@NonNull HttpServletRequest request) {
        return request.getRequestURI().startsWith(DEV_ENV_URI_PREFIX);
    }

    private String getForwardPath(@NonNull HttpServletRequest request, String storeId) {
        String requestURI = request.getRequestURI();
        String relativePath = requestURI.substring(DEV_ENV_URI_PREFIX.length());

        // Remove o storeId do início do relativePath se estiver presente
        // URL: /dev-env/STORE_ID/file.html -> relativePath: /STORE_ID/file.html
        if (relativePath.startsWith("/" + storeId)) {
            relativePath = relativePath.substring(("/" + storeId).length());
        }

        // Se relativePath estiver vazio ou for apenas "/", usar index.html
        if (relativePath.isEmpty() || relativePath.equals("/")) {
            relativePath = "/" + INDEX_HTML;
        }

        return DEV_ENV_RESOURCE_PATH + DEV_ENV_URI_SEPARATOR + storeId + relativePath;
    }

    private void refreshDevEnv(String storeId) {
        final String storePath = DEV_ENV_URI_PREFIX + DEV_ENV_URI_SEPARATOR + storeId;
        File targetFile = new File(DEV_ENV_ROOT_PATH, storePath);
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }
    }

    public void deleteContents(File directory) throws IOException {
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files == null) {
                return;
            }

            for (File file : files) {
                if (file.isDirectory()) {
                    deleteContents(file);
                    java.nio.file.Files.delete(file.toPath());
                } else {
                    java.nio.file.Files.delete(file.toPath());
                }
            }
        }
    }

    private boolean shouldSynchronizeFiles(byte[] storeId, FileManagerEnum environment, String ipAddress) {
        try {
            ULID storeUlid = ULID.fromBytes(storeId);
            final String storePath = DEV_ENV_URI_PREFIX + DEV_ENV_URI_SEPARATOR + storeUlid;
            File targetDir = new File(DEV_ENV_ROOT_PATH, storePath);

            // 🎯 NEW STORE DETECTION: Check if store is new (no active theme or templates)
            if (isNewStore(storeId, environment)) {
                return false;
            }

            if (!targetDir.exists()) {
                return true;
            }

            File[] files = targetDir.listFiles();
            if (files == null || files.length == 0) {
                return true;
            }

            boolean hasIndexHtml = false;
            for (File file : files) {
                if (INDEX_HTML.equalsIgnoreCase(file.getName())) {
                    hasIndexHtml = true;
                    break;
                }
            }

            if (!hasIndexHtml) {
                return true;
            }

            Optional<EnvManagerAuditDBEntity> latestAuditOpt = 
                    auditRepository.findFirstByStoreIdAndEnvironmentOrderByUpdatedAtDesc(storeId, environment);

            if (latestAuditOpt.isEmpty()) {
                return false;
            }

            Optional<EnvVerificationDBEntity> verificationOpt = 
                    verificationRepository.findByStoreIdAndEnvironmentAndIpAddress(storeId, environment, ipAddress);

            if (verificationOpt.isEmpty()) {
                return true;
            }

            LocalDateTime auditTimestamp = latestAuditOpt.get().getUpdatedAt();
            LocalDateTime verificationTimestamp = verificationOpt.get().getVerifiedAt();

            return auditTimestamp.isAfter(verificationTimestamp);

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 🆕 NOVA FUNÇÃO: Detecta se a loja é nova (sem temas ativos ou templates personalizados)
     * Uma loja nova deve mostrar onboarding em vez de servir templates padrão
     */
    private boolean isNewStore(byte[] storeId, FileManagerEnum environment) {
        try {
            ULID storeUlid = ULID.fromBytes(storeId);
            
            // Verificar se há arquivos personalizados registrados no FileManager
            List<FileManager> existingFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeUlid, environment);
            
            // Se não há arquivos registrados, é uma loja nova
            if (existingFiles == null || existingFiles.isEmpty()) {
                return true;
            }
            
            // Verificar se há arquivos HTML/templates personalizados (não apenas arquivos padrão)
            long customHtmlCount = existingFiles.stream()
                    .filter(file -> file.getType() == FileManagerEnum.ARCHIVE)
                    .filter(file -> file.getName() != null && file.getName().toLowerCase().endsWith(".html"))
                    .filter(file -> !file.getName().equals("index.html")) // Index.html pode ser padrão
                    .count();
            
            // Se há templates personalizados, não é loja nova
            if (customHtmlCount > 0) {
                return false;
            }
            
            // Verificar se há audit entries (indicando que a loja foi configurada)
            Optional<EnvManagerAuditDBEntity> latestAuditOpt = 
                    auditRepository.findFirstByStoreIdAndEnvironmentOrderByUpdatedAtDesc(storeId, environment);
            
            // Se não há audit entries ou são muito antigos (>7 dias), considerar nova
            if (latestAuditOpt.isEmpty()) {
                return true;
            }
            
            // Se o último audit é muito antigo, pode ser considerada nova para reset
            LocalDateTime weekAgo = LocalDateTime.now().minusDays(7);
            return latestAuditOpt.get().getUpdatedAt().isBefore(weekAgo);
            
        } catch (Exception e) {
            // Em caso de erro, assume que não é nova para não quebrar funcionalidade
            return false;
        }
    }

    /**
     * 🚀 NOVA FUNÇÃO OTIMIZADA: Sincronização inteligente que evita operações desnecessárias
     */
    private void synchronizeFilesOptimized(byte[] storeId, ULID storeUlid, FileManagerEnum environment, String ipAddress) throws IOException {
        final String storePath = DEV_ENV_URI_PREFIX + DEV_ENV_URI_SEPARATOR + storeUlid;
        File targetDir = new File(DEV_ENV_ROOT_PATH, storePath);

        try {
            // Verificar se o diretório existe e tem conteúdo
            boolean needsFullSync = false;

            if (!targetDir.exists()) {
                boolean created = targetDir.mkdirs();
                if (!created) {
                    throw new IOException("Failed to create directory: " + targetDir.getAbsolutePath());
                }
                needsFullSync = true;
            } else {
                File[] files = targetDir.listFiles();
                if (files == null || files.length == 0) {
                    needsFullSync = true;
                }
            }

            if (needsFullSync) {
                synchronizeFiles(storeId, storeUlid, environment, ipAddress);
            } else {
                // ✅ CORREÇÃO: Quando shouldSync=true, sempre fazer sincronização completa
                // para garantir que novos templates de tema sejam baixados
                synchronizeFiles(storeId, storeUlid, environment, ipAddress);
            }

        } catch (Exception e) {
            // Fallback para sincronização completa em caso de erro
            synchronizeFiles(storeId, storeUlid, environment, ipAddress);
        }
    }



    private void synchronizeFiles(byte[] storeId, ULID storeUlid, FileManagerEnum environment, String ipAddress) throws IOException {
        final String storePath = DEV_ENV_URI_PREFIX + DEV_ENV_URI_SEPARATOR + storeUlid;
        File targetDir = new File(DEV_ENV_ROOT_PATH, storePath);

        try {
            deleteContents(targetDir);

            if (!targetDir.exists()) {
                boolean created = targetDir.mkdirs();
                if (!created) {
                    throw new IOException("Failed to create directory: " + targetDir.getAbsolutePath());
                }
            }
        } catch (IOException e) {
            throw new IOException("Failed to clean up directory: " + targetDir.getAbsolutePath(), e);
        }

        try {
            byte[] zipContent = downloadRootFilesUseCase.execute(storeUlid, environment);
            
            if (zipContent != null && zipContent.length > 0) {
                extractZipContent(zipContent, targetDir);
            } else {
                tryThemeTemplateFallback(storeUlid, targetDir);
            }

            LocalDateTime now = LocalDateTime.now();
            try {
                Optional<EnvVerificationDBEntity> verificationOpt = 
                        verificationRepository.findByStoreIdAndEnvironmentAndIpAddress(storeId, environment, ipAddress);
                
                EnvVerificationDBEntity verification;
                if (verificationOpt.isPresent()) {
                    verification = verificationOpt.get();
                    verification.setVerifiedAt(now);
                } else {
                    verification = EnvVerificationDBEntity.builder()
                            .storeId(storeId)
                            .environment(environment)
                            .ipAddress(ipAddress)
                            .verifiedAt(now)
                            .build();
                }
                
                verificationRepository.save(verification);
            } catch (org.springframework.dao.DataIntegrityViolationException e) {
                // Ignorar violação de chave duplicada - pode acontecer em requisições simultâneas
                if (!e.getMessage().contains("Duplicate entry") && !e.getMessage().contains("idx_env_manager_verification_unique")) {
                    throw e;
                }
            }

            // 🚀 OTIMIZAÇÃO: Sincronização HTML mais leve durante desenvolvimento
            downloadHtmlFilesOptimized(storeUlid, targetDir, environment);
            
            // 🧹 CLEANUP: Remover arquivos vazios do FileManager que podem ter sido criados anteriormente
            cleanupEmptyFilesFromFileManager(storeUlid, environment);

        } catch (Exception e) {
            // Não interromper o fluxo, pois pode ser um problema temporário de sincronização
        }
    }


    /**
     * 🚀 FUNÇÃO OTIMIZADA: Sincronização HTML mais leve para desenvolvimento
     */
    private void downloadHtmlFilesOptimized(ULID storeUlid, File targetDir, FileManagerEnum environment) {
        try {
            // 🚫 PROTEÇÃO: Não sincronizar se loja está em processo de restore
            if (isStoreInRestore(storeUlid.toString())) {
                return;
            }

            // Apenas sincronizar arquivos HTML que já foram baixados via ZIP
            // Pular descoberta de órfãos que é muito lenta
            synchronizeDownloadedHtmlFilesWithDatabase(storeUlid, targetDir, environment);

            // Baixar apenas arquivos HTML que estão registrados no banco mas não estão localmente
            downloadMissingHtmlFilesFromDatabase(storeUlid, targetDir, environment);


        } catch (Exception e) {
            // Fallback para sincronização completa apenas se necessário
            if (targetDir.listFiles() == null || targetDir.listFiles().length == 0) {
                downloadAllHtmlFilesFromS3AndSync(storeUlid, targetDir, environment);
            }
        }
    }

    private void downloadAllHtmlFilesFromS3AndSync(ULID storeUlid, File targetDir, FileManagerEnum environment) {
        try {
            
            // Sincronizar todos os arquivos HTML que já foram baixados via ZIP
            synchronizeDownloadedHtmlFilesWithDatabase(storeUlid, targetDir, environment);
            
            // Tentar baixar arquivos HTML que estão registrados no banco mas não estão localmente
            downloadMissingHtmlFilesFromDatabase(storeUlid, targetDir, environment);
            
            // 🚀 OTIMIZAÇÃO: Descoberta de órfãos desabilitada durante desenvolvimento para melhor performance
            // discoverAndDownloadOrphanHtmlFilesFromS3(storeUlid, targetDir, environment);
            
            
        } catch (Exception e) {
            // Não propaga erro para não quebrar fluxo principal
        }
    }

    /**
     * Baixa arquivos HTML que estão registrados no FileManager mas não existem localmente.
     */
    private void downloadMissingHtmlFilesFromDatabase(ULID storeUlid, File targetDir, FileManagerEnum environment) {
        try {
            // Obter todos os arquivos HTML registrados no FileManager
            List<FileManager> existingFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeUlid, environment);
            
            List<FileManager> htmlFiles = existingFiles.stream()
                    .filter(file -> file.getType() == FileManagerEnum.ARCHIVE)
                    .filter(file -> file.getName() != null && file.getName().toLowerCase().endsWith(".html"))
                    .toList();
            
            // Para cada arquivo HTML registrado, verificar se existe localmente
            for (FileManager htmlFileRecord : htmlFiles) {
                File localFile = new File(targetDir, htmlFileRecord.getName());
                
                if (!localFile.exists()) {
                    try {
                        // Tentar baixar do S3
                        String s3PathTemplate = String.format(S3_HTML_PATH_TEMPLATE, htmlFileRecord.getName());
                        downloadHtmlFileFromS3(storeUlid, targetDir, htmlFileRecord.getName(), s3PathTemplate);
                    } catch (Exception e) {
                        // Continua com próximo arquivo se falhar
                    }
                }
            }
            
        } catch (Exception e) {
            // Não propaga erro para não quebrar fluxo principal
        }
    }







    private void syncIndividualHtmlFileWithDatabase(ULID storeUlid, String fileName, File htmlFile) {
        try {
            List<FileManager> existingFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeUlid, FileManagerEnum.DEVELOP);
            
            boolean fileExistsInDatabase = existingFiles.stream()
                    .anyMatch(file -> file.getType() == FileManagerEnum.ARCHIVE && fileName.equals(file.getName()));
            
            if (!fileExistsInDatabase && htmlFile.exists()) {
                try {
                    String fileContent = Files.readString(htmlFile.toPath(), java.nio.charset.StandardCharsets.UTF_8);
                    
                    // 🚫 PROTEÇÃO: Não sincronizar arquivos vazios ou muito pequenos para o banco
                    if (fileContent != null && fileContent.trim().length() > 100) {
                        createFileUseCase.execute(fileName, fileContent, null, "dev", storeUlid);
                    }
                } catch (Exception e) {
                    // Continua processamento mesmo com erro
                }
            }
            
        } catch (Exception e) {
            // Continua processamento mesmo com erro
        }
    }

    private void synchronizeDownloadedHtmlFilesWithDatabase(ULID storeUlid, File targetDir, FileManagerEnum environment) {
        try {
            // 🚫 PROTEÇÃO: Não sincronizar se loja está em processo de restore
            if (isStoreInRestore(storeUlid.toString())) {
                return;
            }
            
            File[] htmlFiles = targetDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".html"));
            
            if (htmlFiles == null || htmlFiles.length == 0) {
                return; 
            }

            List<FileManager> existingFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeUlid, environment);
            
            java.util.Set<String> existingFileNames = existingFiles.stream()
                    .filter(file -> file.getType() == FileManagerEnum.ARCHIVE)
                    .map(FileManager::getName)
                    .collect(java.util.stream.Collectors.toSet());

            for (File htmlFile : htmlFiles) {
                String fileName = htmlFile.getName();
                
                if (!existingFileNames.contains(fileName)) {
                    try {
                        String fileContent = Files.readString(htmlFile.toPath(), java.nio.charset.StandardCharsets.UTF_8);
                        
                        // 🚫 PROTEÇÃO: Não sincronizar arquivos vazios ou muito pequenos para o banco
                        if (fileContent != null && fileContent.trim().length() > 100) {
                            createFileUseCase.execute(fileName, fileContent, null, "dev", storeUlid);
                        }
                    } catch (Exception e) {
                        // Continua o processamento dos outros arquivos mesmo se um falhar
                    }
                }
            }
            
        } catch (Exception e) {
            // Não propaga a exceção para não quebrar o fluxo principal
        }
    }

    private boolean containsSuspiciousSequences(String fileName) {
        return fileName.contains("../") || 
               fileName.contains("...") || 
               fileName.contains(":$") || 
               fileName.startsWith("/") || 
               fileName.contains(":") || 
               (fileName.contains("\\..") && !fileName.endsWith(".html")) || 
               (fileName.contains("..\\") && !fileName.endsWith(".html"));
    }

    private void extractZipContent(byte[] zipContent, File targetDir) throws IOException {
        try (ZipInputStream zipIn = new ZipInputStream(new ByteArrayInputStream(zipContent))) {
            ZipEntry entry = zipIn.getNextEntry();

            while (entry != null) {
                if (entry.getName() == null || entry.getName().isEmpty()) {
                    zipIn.closeEntry();
                    entry = zipIn.getNextEntry();
                    continue;
                }

                if (containsSuspiciousSequences(entry.getName())) {
                    throw new DevEnvFilterException(StoreErrorMessageEnum.PATH_TRAVERSAL_DETECTED);
                }

                File outputFile = new File(targetDir, entry.getName());

                String canonicalDestinationPath = targetDir.getCanonicalPath();
                String canonicalOutputPath = outputFile.getCanonicalPath();

                if (!canonicalOutputPath.startsWith(canonicalDestinationPath + File.separator) && 
                    !canonicalOutputPath.equals(canonicalDestinationPath)) {
                    throw new DevEnvFilterException(StoreErrorMessageEnum.PATH_TRAVERSAL_DETECTED);
                }

                if (!entry.isDirectory()) {
                    File parent = outputFile.getParentFile();
                    if (parent != null && !parent.exists()) {
                        parent.mkdirs();
                    }

                    try (java.io.FileOutputStream fos = new java.io.FileOutputStream(outputFile)) {
                        byte[] buffer = new byte[8192];
                        int len;
                        while ((len = zipIn.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                } else {
                    if (!outputFile.exists() && !outputFile.mkdirs()) {
                        throw new DevEnvFilterException(StoreErrorMessageEnum.FAILED_TO_CREATE_DIRECTORY);
                    }
                }

                zipIn.closeEntry();
                entry = zipIn.getNextEntry();
            }
        } catch (DevEnvFilterException e) {
            throw e;
        } catch (Exception e) {
            throw new DevEnvFilterException(StoreErrorMessageEnum.FAILED_TO_SYNCHRONIZE_FILES);
        }
    }

    private void downloadHtmlFileFromS3(ULID storeUlid, File targetDir, String fileName, String s3PathTemplate) 
            throws IOException, InterruptedException {
        String s3Url = S3_BASE_URL + "/" + storeUlid.toString() + s3PathTemplate;
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(s3Url))
                .GET()
                .build();

        HttpResponse<byte[]> response = httpClient.send(request, HttpResponse.BodyHandlers.ofByteArray());
        
        if (response.statusCode() == 200) {
            byte[] responseBody = response.body();
            
            // 🚫 PROTEÇÃO: Não criar arquivos vazios no disco
            if (responseBody != null && responseBody.length > 100) {
                File htmlFile = new File(targetDir, fileName);
                Files.write(htmlFile.toPath(), responseBody);
            }
        } else {
            throw new IOException("Failed to download HTML file from S3: " + response.statusCode());
        }
    }

    private boolean shouldUpdateHtmlFileFromS3(ULID storeUlid, File localFile, String s3PathTemplate) throws IOException, InterruptedException {
        String s3Url = S3_BASE_URL + "/" + storeUlid.toString() + s3PathTemplate;
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(s3Url))
                .method("HEAD", HttpRequest.BodyPublishers.noBody())
                .build();

        HttpResponse<Void> response = httpClient.send(request, HttpResponse.BodyHandlers.discarding());
        
        if (response.statusCode() == 200) {
            long s3FileSize = response.headers()
                    .firstValue("Content-Length")
                    .map(Long::parseLong)
                    .orElse(0L);
            
            long localFileSize = localFile.length();
            
            String lastModifiedHeader = response.headers()
                    .firstValue("Last-Modified")
                    .orElse(null);
            
            if (lastModifiedHeader != null) {
                try {
                    java.time.format.DateTimeFormatter formatter = 
                        java.time.format.DateTimeFormatter.RFC_1123_DATE_TIME;
                    java.time.ZonedDateTime s3LastModified = 
                        java.time.ZonedDateTime.parse(lastModifiedHeader, formatter);
                    
                    java.time.Instant localLastModified = 
                        java.time.Instant.ofEpochMilli(localFile.lastModified());
                    
                    return s3LastModified.toInstant().isAfter(localLastModified) || 
                           s3FileSize != localFileSize;
                    
                } catch (Exception e) {
                    return s3FileSize != localFileSize;
                }
            } else {
                return s3FileSize != localFileSize;
            }
        } else {
            return false;
        }
    }
    
    /**
     * 🧹 FUNÇÃO DE CLEANUP: Remove arquivos vazios do FileManager
     */
    private void cleanupEmptyFilesFromFileManager(ULID storeUlid, FileManagerEnum environment) {
        try {
            List<FileManager> existingFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeUlid, environment);
            
            for (FileManager file : existingFiles) {
                if (file.getType() == FileManagerEnum.ARCHIVE && 
                    file.getName() != null && 
                    file.getName().toLowerCase().endsWith(".html")) {
                    
                    // 🚫 PROTEÇÃO ESPECIAL: Não remover index.html durante restore
                    if ("index.html".equals(file.getName()) && isStoreInRestore(storeUlid.toString())) {
                        continue;
                    }
                    
                    try {
                        // Verificar se o arquivo tem conteúdo válido usando FileManagerS3Gateway
                        byte[] contentBytes = fileManagerS3Gateway.getFileContent(file);
                        
                        if (contentBytes == null || contentBytes.length <= 100) {
                            // Arquivo vazio ou muito pequeno - remover
                            fileManagerGateway.deleteById(file.getId());
                        }
                    } catch (Exception e) {
                        // Continuar com próximo arquivo se houver erro
                    }
                }
            }
        } catch (Exception e) {
            // Não propagar erro para não quebrar fluxo principal
        }
    }

    /**
     * 🚀 NOVO FALLBACK: Tenta baixar arquivos do template do tema ativo quando file-manager está vazio
     */
    private boolean tryThemeTemplateFallback(ULID storeUlid, File targetDir) {
        try {
            // Buscar tema ativo
            Theme activeTheme = getActiveThemeUseCase.execute(storeUlid);
            
            if (activeTheme == null) {
                return false;
            }
            
            // Verificar se tema tem template_s3_path
            String templateS3Path = activeTheme.getTemplateS3Path();
            if (templateS3Path == null || templateS3Path.trim().isEmpty()) {
                return false;
            }
            
            // Tentar baixar index.html do template
            String indexS3Url = S3_BASE_URL + "/" + templateS3Path;
            if (!indexS3Url.endsWith("/")) {
                indexS3Url += "/";
            }
            indexS3Url += "index.html";
            
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(indexS3Url))
                    .GET()
                    .build();

            HttpResponse<byte[]> response = httpClient.send(request, HttpResponse.BodyHandlers.ofByteArray());
            
            if (response.statusCode() == 200) {
                byte[] content = response.body();
                if (content != null && content.length > 100) {
                    File indexFile = new File(targetDir, "index.html");
                    Files.write(indexFile.toPath(), content);
                    
                    // Tentar sincronizar com file-manager
                    try {
                        String htmlContent = new String(content, java.nio.charset.StandardCharsets.UTF_8);
                        createFileUseCase.execute("index.html", htmlContent, null, "dev", storeUlid);
                    } catch (Exception e) {
                        // Não é crítico, arquivo local já foi criado
                    }
                    
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
            
        } catch (Exception e) {
            return false;
        }
    }
} 