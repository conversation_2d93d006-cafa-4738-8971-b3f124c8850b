package com.iluria.stores.store.gateway;

import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * Gateway responsável por gerenciar backups de temas no S3.
 * Armazena os arquivos de backup no S3 e mantém apenas metadados no banco.
 */
@Component
@RequiredArgsConstructor
public class ThemeBackupS3Gateway {

    private final S3Client s3Client;
    private final com.iluria.stores.store.service.ThemeS3AsyncService themeS3AsyncService;
    
    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    /**
     * Faz upload dos arquivos de backup para o S3 com compressão ZIP.
     *
     * @param backupS3Path Caminho base do backup no S3
     * @param templateFiles Map com nome do arquivo e conteúdo
     * @return true se o upload foi bem-sucedido
     */
    public boolean uploadBackupFiles(String backupS3Path, Map<String, String> templateFiles) {
        try {
            // Calcular tamanho original dos arquivos
            long originalSize = templateFiles.values().stream()
                    .mapToLong(content -> content.getBytes().length)
                    .sum();
            
            // Criar ZIP comprimido em memória
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            try (ZipOutputStream zos = new ZipOutputStream(baos)) {
                for (Map.Entry<String, String> entry : templateFiles.entrySet()) {
                    String fileName = entry.getKey();
                    String fileContent = entry.getValue();
                    
                    // Criar entrada ZIP
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zos.putNextEntry(zipEntry);
                    
                    // Escrever conteúdo do arquivo
                    zos.write(fileContent.getBytes());
                    zos.closeEntry();
                }
            }
            
            byte[] compressedData = baos.toByteArray();
            long compressedSize = compressedData.length;
            double compressionRatio = ((double) (originalSize - compressedSize) / originalSize) * 100;
            
            // Upload do arquivo ZIP para S3
            String zipKey = backupS3Path + "/backup.zip";
            
            Map<String, String> metadata = new HashMap<>();
            metadata.put("backup-compressed", "true");
            metadata.put("file-count", String.valueOf(templateFiles.size()));
            metadata.put("original-size", String.valueOf(originalSize));
            metadata.put("compressed-size", String.valueOf(compressedSize));
            metadata.put("compression-ratio", String.format("%.1f%%", compressionRatio));
            metadata.put("backup-version", "2.0");
            
            PutObjectRequest putRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(zipKey)
                    .contentType("application/zip")
                    .metadata(metadata)
                    .build();
            
            s3Client.putObject(putRequest, RequestBody.fromBytes(compressedData));
            
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Baixa os arquivos de backup do S3 com descompressão automática.
     *
     * @param backupS3Path Caminho do backup no S3
     * @return Map com nome do arquivo e conteúdo
     */
    public Map<String, String> downloadBackupFiles(String backupS3Path) {
        Map<String, String> templateFiles = new HashMap<>();
        
        try {
            // Primeiro, tentar baixar backup comprimido (novo formato)
            String zipKey = backupS3Path + "/backup.zip";
            
            try {
                // Verificar se existe backup comprimido
                GetObjectRequest zipRequest = GetObjectRequest.builder()
                        .bucket(bucketName)
                        .key(zipKey)
                        .build();
                
                byte[] compressedData = s3Client.getObjectAsBytes(zipRequest).asByteArray();
                
                // Descomprimir o ZIP
                ByteArrayInputStream bais = new ByteArrayInputStream(compressedData);
                
                try (ZipInputStream zis = new ZipInputStream(bais)) {
                    ZipEntry entry;
                    while ((entry = zis.getNextEntry()) != null) {
                        if (!entry.isDirectory()) {
                            String fileName = entry.getName();
                            
                            // Ler conteúdo do arquivo
                            ByteArrayOutputStream fileContent = new ByteArrayOutputStream();
                            byte[] buffer = new byte[4096];
                            int bytesRead;
                            
                            while ((bytesRead = zis.read(buffer)) != -1) {
                                fileContent.write(buffer, 0, bytesRead);
                            }
                            
                            templateFiles.put(fileName, new String(fileContent.toByteArray(), StandardCharsets.UTF_8));
                        }
                        zis.closeEntry();
                    }
                }
                
            } catch (NoSuchKeyException e) {
                // Backup comprimido não existe, tentar formato antigo (arquivos individuais)
                templateFiles = downloadLegacyBackupFiles(backupS3Path);
            }
            
        } catch (Exception e) {
            // Return empty map on error
        }
        
        return templateFiles;
    }
    
    /**
     * Baixa arquivos de backup no formato legado (arquivos individuais).
     * Mantido para compatibilidade com backups antigos.
     */
    private Map<String, String> downloadLegacyBackupFiles(String backupS3Path) {
        Map<String, String> templateFiles = new HashMap<>();
        
        try {
            // Listar arquivos no caminho do backup
            ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(backupS3Path + "/")
                    .build();
            
            ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);
            
            for (S3Object s3Object : listResponse.contents()) {
                String objectKey = s3Object.key();
                
                // Pular diretório e arquivo ZIP (se existir)
                if (objectKey.equals(backupS3Path + "/") || objectKey.endsWith("/backup.zip")) {
                    continue;
                }
                
                try {
                    // Extrair nome do arquivo
                    String fileName = objectKey.substring(objectKey.lastIndexOf("/") + 1);
                    
                    // Baixar conteúdo do arquivo
                    GetObjectRequest getRequest = GetObjectRequest.builder()
                            .bucket(bucketName)
                            .key(objectKey)
                            .build();
                    
                    String fileContent = s3Client.getObjectAsBytes(getRequest).asUtf8String();
                    templateFiles.put(fileName, fileContent);
                    
                } catch (Exception e) {
                    // Continue with next file
                }
            }
            
        } catch (Exception e) {
            // Return what we got so far
        }
        
        return templateFiles;
    }

    /**
     * Remove um backup do S3 (suporta formatos comprimido e legado).
     *
     * @param backupS3Path Caminho do backup no S3
     * @return true se a remoção foi bem-sucedida
     */
    public boolean deleteBackupFiles(String backupS3Path) {
        try {
            // Listar todos os arquivos no caminho do backup
            ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(backupS3Path + "/")
                    .build();
            
            ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);
            
            if (listResponse.contents().isEmpty()) {
                return true;
            }
            
            // Preparar lista de objetos para deletar
            Delete delete = Delete.builder()
                    .objects(listResponse.contents().stream()
                            .map(s3Object -> ObjectIdentifier.builder()
                                    .key(s3Object.key())
                                    .build())
                            .toList())
                    .build();
            
            DeleteObjectsRequest deleteRequest = DeleteObjectsRequest.builder()
                    .bucket(bucketName)
                    .delete(delete)
                    .build();
            
            s3Client.deleteObjects(deleteRequest);
            
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Verifica se um backup existe no S3 (suporta formatos comprimido e legado).
     *
     * @param backupS3Path Caminho do backup no S3
     * @return true se o backup existe
     */
    public boolean backupExists(String backupS3Path) {
        try {
            // Primeiro, verificar se existe backup comprimido
            String zipKey = backupS3Path + "/backup.zip";
            
            try {
                s3Client.getObjectAttributes(GetObjectAttributesRequest.builder()
                        .bucket(bucketName)
                        .key(zipKey)
                        .objectAttributes(ObjectAttributes.OBJECT_SIZE)
                        .build());
                
                return true; // Backup comprimido existe
                
            } catch (NoSuchKeyException e) {
                // Backup comprimido não existe, verificar formato legado
                ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                        .bucket(bucketName)
                        .prefix(backupS3Path + "/")
                        .maxKeys(1)
                        .build();
                
                ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);
                return !listResponse.contents().isEmpty();
            }
            
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Calcula o tamanho total de um backup no S3 (suporta formatos comprimido e legado).
     *
     * @param backupS3Path Caminho do backup no S3
     * @return Tamanho total em bytes
     */
    public long calculateBackupSize(String backupS3Path) {
        try {
            // Primeiro, verificar se existe backup comprimido
            String zipKey = backupS3Path + "/backup.zip";
            
            try {
                GetObjectAttributesRequest zipRequest = GetObjectAttributesRequest.builder()
                        .bucket(bucketName)
                        .key(zipKey)
                        .objectAttributes(ObjectAttributes.OBJECT_SIZE)
                        .build();
                
                GetObjectAttributesResponse zipResponse = s3Client.getObjectAttributes(zipRequest);
                return zipResponse.objectSize();
                
            } catch (NoSuchKeyException e) {
                // Backup comprimido não existe, calcular tamanho do formato legado
                ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                        .bucket(bucketName)
                        .prefix(backupS3Path + "/")
                        .build();
                
                ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);
                
                return listResponse.contents().stream()
                        .mapToLong(S3Object::size)
                        .sum();
            }
                    
        } catch (Exception e) {
            return 0L;
        }
    }

    /**
     * Lista todos os backups de uma store no S3.
     *
     * @param storeId ID da store
     * @return Lista de caminhos de backup
     */
    public java.util.List<String> listStoreBackups(ULID storeId) {
        try {
            String storePrefix = String.format("stores/%s/themes/", storeId.toString());
            
            ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(storePrefix)
                    .delimiter("/")
                    .build();
            
            ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);
            
            return listResponse.commonPrefixes().stream()
                    .map(CommonPrefix::prefix)
                    .filter(prefix -> prefix.contains("/backups/"))
                    .toList();
                    
        } catch (Exception e) {
            return java.util.List.of();
        }
    }
}
