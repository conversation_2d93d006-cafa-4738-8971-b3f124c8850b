package com.iluria.stores.store.usecase.theme;

import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;


@Service
@RequiredArgsConstructor
public class CopyDefaultThemeUseCase {

    private final ThemeGateway themeGateway;
    private final ThemeTemplateManagerUseCase themeTemplateManagerUseCase;

    /**
     * Copia um tema padrão global para uma loja específica
     * Usado quando o usuário seleciona um tema padrão como base
     */
    @Transactional
    public Theme execute(ULID defaultThemeId, ULID storeId, String customName, String customDescription) {
        if (defaultThemeId == null) {
            throw new IllegalArgumentException("Default theme ID cannot be null");
        }
        if (storeId == null) {
            throw new IllegalArgumentException("Store ID cannot be null");
        }

        // Buscar o tema padrão
        Theme defaultTheme = themeGateway.findById(defaultThemeId);
        if (defaultTheme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }

        // Verificar se é realmente um tema padrão
        if (!defaultTheme.getIsDefault()) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_TEMPLATE_CONFIG);
        }

        // Verificar se já existe um tema com o mesmo nome na loja
        String baseName = customName != null ? customName : defaultTheme.getName() + " - Personalizado";
        String finalName = baseName;
        
        // Se já existe, gerar nome sequencial (Personalizado 2, Personalizado 3, etc.)
        int counter = 2;
        while (themeGateway.existsByStoreIdAndName(storeId, finalName)) {
            if (customName != null) {
                finalName = customName + " " + counter;
            } else {
                finalName = defaultTheme.getName() + " - Personalizado " + counter;
            }
            counter++;
            
            // Limite de segurança para evitar loop infinito
            if (counter > 100) {
                finalName = defaultTheme.getName() + " - Cópia " + System.currentTimeMillis();
                break;
            }
        }

        // Criar cópia para a loja
        Theme copiedTheme = copyThemeForStore(defaultTheme, storeId, finalName, customDescription);
        
        // Salvar o tema copiado primeiro
        Theme savedTheme = themeGateway.save(copiedTheme);
        
        // Copiar os arquivos de template do tema padrão para o novo path
        if (defaultTheme.getHasTemplateFiles() && defaultTheme.getTemplateFiles() != null && !defaultTheme.getTemplateFiles().isEmpty()) {
            copyTemplateFiles(defaultTheme, savedTheme);
            // Salvar novamente com as informações de template atualizadas
            savedTheme = themeGateway.save(savedTheme);
        }
        
        return savedTheme;
    }

    /**
     * Cria uma cópia de um tema padrão para uma loja específica
     */
    private Theme copyThemeForStore(Theme defaultTheme, ULID storeId, String customName, String customDescription) {
        ULID newThemeId = ULID.random(); // Novo ID para a cópia
        return Theme.builder()
                .id(newThemeId)
                .storeId(storeId) // Associar à loja
                .name(customName)
                .description(customDescription != null ? customDescription : defaultTheme.getDescription())
                .categoryId(defaultTheme.getCategoryId()) // Cópia do category ID
                .version(defaultTheme.getVersion())
                .author(defaultTheme.getAuthor())
                .isPremium(false) // Cópias não são premium
                .isDefault(false) // Cópias não são mais "default"
                .isActive(false) // Não ativar automaticamente
                .cssVariables(defaultTheme.getCssVariables())
                .typography(defaultTheme.getTypography())
                .spacing(defaultTheme.getSpacing())
                .customizations(defaultTheme.getCustomizations())
                .previewImageUrl(defaultTheme.getPreviewImageUrl())
                // ✅ GERAR PATH ÚNICO PARA EVITAR SOBRESCREVER TEMPLATES PADRÃO
                .templateS3Path("stores/" + storeId + "/themes/" + newThemeId + "/templates/")
                .templateFiles(defaultTheme.getTemplateFiles())
                .templateVersion(defaultTheme.getTemplateVersion())
                .templateChecksum(defaultTheme.getTemplateChecksum())
                .hasTemplateFiles(defaultTheme.getHasTemplateFiles())
                .templateUpdatedAt(defaultTheme.getTemplateUpdatedAt())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * Copia os arquivos de template do tema padrão para o path único do tema copiado
     */
    private void copyTemplateFiles(Theme sourceTheme, Theme destTheme) {
        try {
            // Baixar os templates do tema padrão
            Map<String, String> templateFiles = themeTemplateManagerUseCase.downloadTemplateFiles(sourceTheme.getId());
            
            if (templateFiles != null && !templateFiles.isEmpty()) {
                // Upload dos templates para o novo path
                themeTemplateManagerUseCase.uploadTemplateFiles(
                    destTheme.getId(), 
                    templateFiles, 
                    sourceTheme.getTemplateVersion()
                );
            }
        } catch (Exception e) {
            // Se falhar ao copiar templates, não falha a operação toda
            // O ApplyThemeTemplateUseCase tentará baixar depois
        }
    }
}
