package com.iluria.stores.store.gateway.db.entity;

import java.time.LocalDateTime;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.iluria.stores.store.domain.StorePhysicalType;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
    
@Entity
@Table(name = "store_physical_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StorePhysicalDataDBEntity {
    @Id
    @Column(name = "id")
    private byte[] id;
    @Column(name = "store_id")
    private byte[] storeId;
    @Column(name = "store_name")
    private String storeName;
    @Enumerated(EnumType.STRING)
    @Column(name = "store_type")
    private StorePhysicalType storeType;
    @Column(name = "document")
    private String document;
    @Column(name = "email")
    private String email;
    @Column(name = "phone")
    private String phone;
    @Column(name = "address")
    private String address;
    @Column(name = "complement")
    private String complement;
    @Column(name = "city")
    private String city;
    @Column(name = "state")
    private String state;
    @Column(name = "zip_code")
    private String zipCode;
    @Column(name = "country")
    private String country;
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

}
