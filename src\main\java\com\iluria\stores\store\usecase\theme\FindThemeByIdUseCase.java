package com.iluria.stores.store.usecase.theme;

import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeGateway;
import com.iluria.exception.StoreErrorMessageEnum;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FindThemeByIdUseCase {

    private final ThemeGateway gateway;

    public Theme execute(ULID themeId, ULID storeId) {
        Theme theme = gateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        if (!theme.getStoreId().equals(storeId)) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        return theme;
    }
    
    public Theme findById(ULID themeId) {
        Theme theme = gateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        return theme;
    }
}