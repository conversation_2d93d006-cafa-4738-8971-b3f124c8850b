package com.iluria.stores.store.entrypoint.controller;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.iluria.stores.store.domain.StorePhysicalData;
import com.iluria.stores.store.entrypoint.controller.dto.StorePhysicalDataDTO;
import com.iluria.stores.store.entrypoint.controller.mapper.StorePhysicalDataDTOMapper;
import com.iluria.stores.store.usecase.storePhysicalData.FindStorePhysicalDataUseCase;
import com.iluria.stores.store.usecase.storePhysicalData.SaveStorePhysicalDataUseCase;

import java.util.Optional;
@RestController
@RequestMapping("/api/store/physical-data")
@RequiredArgsConstructor
public class StorePhysicalDataController {
    private final FindStorePhysicalDataUseCase findStorePhysicalDataUseCase;
    private final SaveStorePhysicalDataUseCase saveStorePhysicalDataUseCase;
    private final StorePhysicalDataDTOMapper mapper;
    @GetMapping
    public ResponseEntity<StorePhysicalDataDTO> getStorePhysicalData() {
        Optional<StorePhysicalData> storePhysicalData = findStorePhysicalDataUseCase.execute();
        if (storePhysicalData.isPresent()) {
            return ResponseEntity.ok(mapper.toDTO(storePhysicalData.get()));
        } else {
            // Retorna um objeto vazio em vez de 404
            StorePhysicalDataDTO emptyResponse = new StorePhysicalDataDTO();
            return ResponseEntity.ok(emptyResponse);
        }
    }
    @PostMapping
    public ResponseEntity<StorePhysicalDataDTO> createStorePhysicalData(
            @RequestBody StorePhysicalDataDTO request) {
        StorePhysicalData storePhysicalData = mapper.toModel(request);
        StorePhysicalData savedData = saveStorePhysicalDataUseCase.execute(storePhysicalData);
        return new ResponseEntity<>(mapper.toDTO(savedData), HttpStatus.CREATED);
    }
    @PutMapping
    public ResponseEntity<StorePhysicalDataDTO> updateStorePhysicalData(
            @RequestBody StorePhysicalDataDTO request) {
        // Buscar dados existentes para manter o ID
        Optional<StorePhysicalData> existingData = findStorePhysicalDataUseCase.execute();
        StorePhysicalData storePhysicalData = mapper.toModel(request);
        // Se existir dados anteriores, manter o ID
        if (existingData.isPresent()) {
            storePhysicalData.setId(existingData.get().getId());
            storePhysicalData.setCreatedAt(existingData.get().getCreatedAt());
        }
        StorePhysicalData savedData = saveStorePhysicalDataUseCase.execute(storePhysicalData);
        return ResponseEntity.ok(mapper.toDTO(savedData));
    }
}