package com.iluria.stores.store.domain;

import java.time.LocalDateTime;

import io.github.jaspeen.ulid.ULID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StorePhysicalData {
    private ULID id;
    private ULID storeId;
    private String storeName;
    private StorePhysicalType storeType;
    private String document;
    private String email;
    private String phone;
    private String address;
    private String complement;
    private String city;
    private String state;
    private String zipCode;
    private String country;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

}
