package com.iluria.stores.store.usecase.theme;

import com.iluria.commons.domain.FileManagerEnum;
import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeGateway;
import com.iluria.stores.store.util.FileSecurityValidator;
import com.iluria.stores.store.service.ThemeErrorHandler;
import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.commons.usecase.fileManager.CreateFileUseCase;
import com.iluria.commons.gateway.FileManagerGateway;
import com.iluria.commons.domain.FileManager;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Set;
import java.util.HashSet;
import java.util.Map;
import java.util.HashMap;

@Service
@RequiredArgsConstructor
public class ThemeTemplateManagerUseCase {

    private final ThemeGateway themeGateway;
    private final S3Client s3Client;
    private final CopyThemeTemplatesToFileManagerUseCase copyThemeTemplatesToFileManagerUseCase;
    private final SyncThemeTemplatesUseCase syncThemeTemplatesUseCase;
    private final ThemeDevEnvSyncUseCase themeDevEnvSyncUseCase;
    private final ThemeErrorHandler themeErrorHandler;
    private final CreateFileUseCase createFileUseCase;
    private final FileManagerGateway fileManagerGateway;

    @Transactional
    public Theme uploadTemplateFilesForRestore(ULID themeId, ULID storeId, Map<String, String> templateFiles, String templateVersion) {
        Theme theme = themeGateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        try {
            // Validate all file names before processing
            Map<String, String> sanitizedFiles = new HashMap<>();
            for (Map.Entry<String, String> entry : templateFiles.entrySet()) {
                String sanitizedName = FileSecurityValidator.validateAndSanitizeFileName(entry.getKey());
                sanitizedFiles.put(sanitizedName, entry.getValue());
            }
            
            // Upload files DIRECTLY to file-manager/dev path instead of templates path
            uploadFilesToFileManagerPath(storeId, themeId, sanitizedFiles);
            
            // Calculate checksum for template integrity
            String checksum = calculateTemplateChecksum(sanitizedFiles);
            
            // Update theme with template information
            Set<String> fileNames = new HashSet<>(sanitizedFiles.keySet());
            theme.updateTemplateFiles(fileNames);
            theme.updateTemplateVersion(templateVersion);
            theme.updateTemplateChecksum(checksum);
            theme.markAsHavingTemplateFiles();
            
            Theme savedTheme = themeGateway.save(theme);
            
            return savedTheme;
            
        } catch (Exception e) {
            themeErrorHandler.handleError(storeId, "upload_template", e);
            throw new ThemeException(themeErrorHandler.getErrorMessage("upload_template", e));
        }
    }

    @Transactional
    public Theme uploadTemplateFiles(ULID themeId, Map<String, String> templateFiles, String templateVersion) {
        Theme theme = themeGateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        try {
            // Validate that we're not overwriting default templates
            validateTemplateS3Path(theme.getTemplateS3FullPath());
            
            // Validate and sanitize all file names before processing
            Map<String, String> sanitizedFiles = new HashMap<>();
            for (Map.Entry<String, String> entry : templateFiles.entrySet()) {
                String sanitizedName = FileSecurityValidator.validateAndSanitizeFileName(entry.getKey());
                sanitizedFiles.put(sanitizedName, entry.getValue());
            }
            
            // Upload files to S3 using theme's S3 path structure
            Set<String> uploadedFiles = uploadFilesToS3(theme, sanitizedFiles);
            
            // Calculate checksum for template integrity
            String checksum = calculateTemplateChecksum(sanitizedFiles);
            
            // Update theme with template information
            theme.updateTemplateFiles(uploadedFiles);
            theme.updateTemplateVersion(templateVersion);
            theme.updateTemplateChecksum(checksum);
            theme.markAsHavingTemplateFiles();
            
            Theme savedTheme = themeGateway.save(theme);
            
            // Copy templates to FileManager for immediate availability
            CopyThemeTemplatesToFileManagerUseCase.CopyTemplateOptions copyOptions = 
                    CopyThemeTemplatesToFileManagerUseCase.CopyTemplateOptions.builder()
                            .environment(FileManagerEnum.DEVELOP)
                            .replaceExisting(true)
                            .processVariables(true)
                            .failOnError(false)
                            .build();
            
            copyThemeTemplatesToFileManagerUseCase.execute(themeId, theme.getStoreId(), copyOptions);

            // Trigger DevEnvFilter synchronization after template update
            themeDevEnvSyncUseCase.triggerSyncAfterTemplateUpdate(theme.getStoreId(), theme.getName());
            
            return savedTheme;
            
        } catch (Exception e) {
            themeErrorHandler.handleError(theme.getStoreId(), "upload_template", e);
            throw new ThemeException(themeErrorHandler.getErrorMessage("upload_template", e));
        }
    }

    /**
     * Upload de templates para um tema durante a inicialização da loja.
     * NÃO copia para o File Manager - apenas salva no S3 para uso futuro.
     * Usado pelo InitializeDefaultThemesUseCase para evitar criar arquivos HTML 
     * automaticamente no file-manager durante o setup da loja.
     */
    @Transactional
    public Theme uploadTemplateFilesForInitialization(ULID themeId, Map<String, String> templateFiles, String templateVersion) {
        Theme theme = themeGateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        try {
            // Validar que não estamos tentando sobrescrever templates padrão
            validateTemplateS3Path(theme.getTemplateS3FullPath());
            
            // Upload files to S3 using theme's S3 path structure
            Set<String> uploadedFiles = uploadFilesToS3(theme, templateFiles);
            
            // Calculate checksum for template integrity
            String checksum = calculateTemplateChecksum(templateFiles);
            
            // Update theme with template information
            theme.updateTemplateFiles(uploadedFiles);
            theme.updateTemplateVersion(templateVersion);
            theme.updateTemplateChecksum(checksum);
            theme.markAsHavingTemplateFiles();
            
            Theme savedTheme = themeGateway.save(theme);
            
            // NÃO copiar para o File Manager durante a inicialização
            // Os templates ficam disponíveis no S3 apenas para quando o usuário escolher o tema
            
            return savedTheme;
            
        } catch (Exception e) {
            themeErrorHandler.handleError(theme.getStoreId(), "upload_template", e);
            throw new ThemeException(themeErrorHandler.getErrorMessage("upload_template", e));
        }
    }
    
    @Transactional
    public Theme syncTemplateFiles(ULID themeId) {
        Theme theme = themeGateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        if (!theme.hasValidTemplateConfiguration()) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_TEMPLATE_CONFIG);
        }
        
        try {
            // Use the new sync use case to synchronize templates
            SyncThemeTemplatesUseCase.SyncOptions syncOptions = SyncThemeTemplatesUseCase.SyncOptions.builder()
                    .environment(FileManagerEnum.DEVELOP)
                    .forceUpdate(false)
                    .removeOrphaned(true)
                    .validateChecksums(true)
                    .build();
            
            syncThemeTemplatesUseCase.execute(themeId, theme.getStoreId(), syncOptions);

            // Refresh theme from database to get updated template information
            Theme updatedTheme = themeGateway.findById(themeId);
            
            // Trigger DevEnvFilter synchronization after template sync
            themeDevEnvSyncUseCase.triggerSyncAfterTemplateUpdate(theme.getStoreId(), theme.getName());
            
            return updatedTheme;
            
        } catch (Exception e) {
            themeErrorHandler.handleError(theme.getStoreId(), "sync_template", e);
            throw new ThemeException(themeErrorHandler.getErrorMessage("sync_template", e));
        }
    }
    
    public Map<String, String> downloadTemplateFiles(ULID themeId) {
        Theme theme = themeGateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        if (!theme.hasValidTemplateConfiguration()) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_TEMPLATE_CONFIG);
        }
        
        try {
            Map<String, String> templateFiles = new HashMap<>();
            
            for (String templateFile : theme.getTemplateFiles()) {
                String content = downloadTemplateFile(theme, templateFile);
                templateFiles.put(templateFile, content);
            }
            
            return templateFiles;
            
        } catch (Exception e) {
            themeErrorHandler.handleError(theme.getStoreId(), "download_template", e);
            throw new ThemeException(themeErrorHandler.getErrorMessage("download_template", e));
        }
    }
    
    @Transactional
    public Theme removeTemplateFiles(ULID themeId) {
        Theme theme = themeGateway.findById(themeId);
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        try {
            // Remove template files from FileManager first
            copyThemeTemplatesToFileManagerUseCase.removeTemplateFiles(themeId, theme.getStoreId(), FileManagerEnum.DEVELOP);
            
            // Remove files from S3 if they exist
            if (theme.hasValidTemplateConfiguration()) {
                deleteS3TemplateFiles(theme);
            }
            
            // Clear template configuration from theme
            theme.markAsNotHavingTemplateFiles();
            
            Theme savedTheme = themeGateway.save(theme);
            
            // Trigger DevEnvFilter synchronization after template removal
            themeDevEnvSyncUseCase.triggerSyncAfterTemplateUpdate(theme.getStoreId(), "Template files removed for theme: " + theme.getName());
            
            return savedTheme;
            
        } catch (Exception e) {
            themeErrorHandler.handleError(theme.getStoreId(), "delete_template", e);
            throw new ThemeException(themeErrorHandler.getErrorMessage("delete_template", e));
        }
    }
    
    /**
     * Upload files directly to file-manager/dev path for restore operations
     */
    private void uploadFilesToFileManagerPath(ULID storeId, ULID themeId, Map<String, String> templateFiles) {
        for (Map.Entry<String, String> entry : templateFiles.entrySet()) {
            String fileName = entry.getKey();
            String content = entry.getValue();
            
            try {
                // Use CreateFileUseCase to properly create both S3 files AND database records
                FileManager createdFile = createFileUseCase.execute(fileName, content, null, "dev", storeId);
                
                // Associate the file with the theme by setting templateId
                if (createdFile != null && createdFile.getId() != null) {
                    createdFile.setTemplateId(themeId);
                    fileManagerGateway.save(createdFile);
                }
                
            } catch (Exception e) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_UPLOAD_FAILED);
            }
        }
    }

    private Set<String> uploadFilesToS3(Theme theme, Map<String, String> templateFiles) {
        Set<String> uploadedFiles = new HashSet<>();
        
        for (Map.Entry<String, String> entry : templateFiles.entrySet()) {
            String fileName = entry.getKey();
            String content = entry.getValue();
            
            try {
                // Use theme's method to get the correct S3 path for each file
                String s3Key = theme.getTemplateFileS3Path(fileName);
                // Validate S3 path for security
                s3Key = FileSecurityValidator.validateTemplatePath(s3Key, theme.getStoreId().toString());
                
                PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                        .bucket(getBucketName())
                        .key(s3Key)
                        .contentType(getContentType(fileName))
                        .build();
                
                s3Client.putObject(putObjectRequest, RequestBody.fromString(content, StandardCharsets.UTF_8));
                uploadedFiles.add(fileName);
            } catch (Exception e) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_UPLOAD_FAILED);
            }
        }
        
        return uploadedFiles;
    }
    
    
    private String downloadTemplateFile(Theme theme, String templateFile) throws IOException {
        String s3Key = theme.getTemplateFileS3Path(templateFile);
        
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(getBucketName())
                .key(s3Key)
                .build();
        
        try (ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest)) {
            return new String(s3Object.readAllBytes(), StandardCharsets.UTF_8);
        }
    }
    
    private void deleteS3TemplateFiles(Theme theme) {
        try {
            Set<String> templateFiles = theme.getTemplateFiles();
            if (templateFiles == null || templateFiles.isEmpty()) {
                return;
            }
            
            for (String templateFile : templateFiles) {
                try {
                    String s3Key = theme.getTemplateFileS3Path(templateFile);
                    s3Client.deleteObject(builder -> builder.bucket(getBucketName()).key(s3Key));
                } catch (Exception e) {
                    // Continue with other files even if one fails
                }
            }
            
        } catch (Exception e) {
        }
    }
    
    private String calculateTemplateChecksum(Map<String, String> templateFiles) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            
            // Create a deterministic string representation of all files
            StringBuilder allContent = new StringBuilder();
            templateFiles.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> {
                        allContent.append(entry.getKey()).append(":").append(entry.getValue());
                    });
            
            byte[] hash = md.digest(allContent.toString().getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
            
        } catch (NoSuchAlgorithmException e) {
            return "unknown";
        }
    }
    
    private String getContentType(String fileName) {
        if (fileName.endsWith(".html")) {
            return "text/html";
        } else if (fileName.endsWith(".css")) {
            return "text/css";
        } else if (fileName.endsWith(".js")) {
            return "application/javascript";
        } else if (fileName.endsWith(".json")) {
            return "application/json";
        } else {
            return "text/plain";
        }
    }
    
    private String getBucketName() {
        return "iluria-bucket-dev";
    }
    
    /**
     * Valida que não estamos tentando sobrescrever templates padrão globais
     */
    private void validateTemplateS3Path(String s3Path) {
        if (s3Path != null && s3Path.startsWith("themes/") && !s3Path.contains("/stores/")) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_TEMPLATE_UPLOAD_FAILED);
        }
    }
}