create table store_physical_data (
    id BINARY(16) PRIMARY KEY,
    store_id BINARY(16) NOT NULL,
    store_name VARCHAR(255) NOT NULL,
    store_type ENUM('LEGAL_ENTITY', 'LEGAL_PERSON') NOT NULL DEFAULT 'LEGAL_PERSON',
    document VARCHAR(255) NOT NULL,
    email VA<PERSON>HAR(255) NOT NULL,
    phone VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    city VARCHAR(255) NOT NULL,
    state VARCHAR(255) NOT NULL,
    zip_code VARCHAR(255) NOT NULL,
    country VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    CONSTRAINT fk_store_data_store_id 
        FOREIGN KEY (store_id) REFERENCES store(id) ON DELETE CASCADE,
    CONSTRAINT uk_store_data_store_id 
        UNIQUE (store_id)
) ENGINE=InnoDB;