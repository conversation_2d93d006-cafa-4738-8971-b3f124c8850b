package com.iluria.stores.store.service;

import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.domain.ThemeBackup;
import com.iluria.stores.store.gateway.ThemeBackupGateway;
import com.iluria.stores.store.gateway.ThemeBackupS3Gateway;
import com.iluria.stores.store.usecase.theme.FindThemeByIdUseCase;
import com.iluria.stores.store.usecase.theme.ThemeTemplateManagerUseCase;
import com.iluria.stores.store.usecase.theme.GetActiveThemeUseCase;
import com.iluria.stores.store.usecase.theme.ActivateThemeUseCase;
import com.iluria.stores.store.usecase.theme.UpdateThemePreviewUseCase;
import com.iluria.stores.store.service.ThemeLockService;
import com.iluria.stores.store.service.ThemeErrorHandler;
import com.iluria.commons.gateway.FileManagerGateway;
import com.iluria.commons.gateway.FileManagerS3Gateway;
import com.iluria.commons.domain.FileManager;
import com.iluria.commons.domain.FileManagerEnum;
import com.iluria.commons.usecase.fileManager.CreateFileUseCase;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ThemeBackupService {

    private final ThemeBackupGateway backupGateway;
    private final ThemeBackupS3Gateway themeBackupS3Gateway;
    private final FindThemeByIdUseCase findThemeByIdUseCase;
    private final ThemeTemplateManagerUseCase templateManagerUseCase;
    private final ThemeProgressService progressService;
    private final GetActiveThemeUseCase getActiveThemeUseCase;
    private final ActivateThemeUseCase activateThemeUseCase;
    private final FileManagerGateway fileManagerGateway;
    private final FileManagerS3Gateway fileManagerS3Gateway;
    private final CreateFileUseCase createFileUseCase;
    private final ThemeLockService themeLockService;
    private final ThemeErrorHandler themeErrorHandler;
    private final UpdateThemePreviewUseCase updateThemePreviewUseCase;

    private static final int MAX_BACKUPS_PER_THEME = 5; // Máximo de backups por tema

    /**
     * Cria backup automático antes de mudança de tema
     */
    @Transactional
    public ThemeBackup createAutomaticBackup(ULID storeId, ULID currentThemeId, String reason) {
        try {

            // Notificar progresso
            progressService.sendBackupCreating(storeId);

            // Buscar tema atual
            Theme currentTheme;
            try {
                currentTheme = findThemeByIdUseCase.execute(currentThemeId, storeId);
                if (currentTheme == null) {
                    return null;
                }
            } catch (Exception e) {
                themeErrorHandler.handleError(storeId, "find_theme", e);
                return null;
            }

            // Verificar se tema tem templates para backup
            if (!currentTheme.getHasTemplateFiles() || currentTheme.getTemplateFiles() == null || currentTheme.getTemplateFiles().isEmpty()) {
                return null;
            }

            // PRIMEIRO: Sincronizar arquivos locais com S3 antes do backup
            try {
                templateManagerUseCase.syncTemplateFiles(currentThemeId);
            } catch (Exception e) {
                themeErrorHandler.handleError(storeId, "sync_template", e);
            }

            // SEGUNDO: Baixar apenas arquivos HTML reais do FileManager (servidos pelo DevEnvFilter)
            Map<String, String> backupFiles;
            try {
                List<FileManager> devFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeId, FileManagerEnum.DEVELOP);

                backupFiles = new java.util.HashMap<>();
                for (FileManager file : devFiles) {
                    // Verificar se é arquivo HTML
                    if (file.getName().endsWith(".html") && file.getType() == FileManagerEnum.ARCHIVE) {
                        try {
                            // Baixar conteúdo do arquivo do S3 usando FileManagerS3Gateway
                            byte[] contentBytes = fileManagerS3Gateway.getFileContent(file);
                            if (contentBytes != null && contentBytes.length > 0) {
                                String content = new String(contentBytes, java.nio.charset.StandardCharsets.UTF_8);
                                if (!content.trim().isEmpty()) {
                                    // Colocar arquivo diretamente na raiz do backup (sem pasta)
                                    backupFiles.put(file.getName(), content);
                                }
                            }
                        } catch (Exception e) {
                            themeErrorHandler.handleError(storeId, "download_file", e);
                            // Continue with next file
                        }
                    }
                }
            } catch (Exception e) {
                return null;
            }

            // Verificar se temos arquivos para backup
            if (backupFiles.isEmpty()) {
                return null;
            }


            // QUINTO: Gerar caminho S3 para o backup
            String backupS3Path = generateBackupS3Path(storeId, currentThemeId);

            // SEXTO: Fazer upload dos arquivos para S3
            boolean uploadSuccess = themeBackupS3Gateway.uploadBackupFiles(backupS3Path, backupFiles);
            if (!uploadSuccess) {
                return null;
            }

            // SÉTIMO: Calcular tamanho real do backup no S3 (pós-compressão)
            long actualBackupSize = themeBackupS3Gateway.calculateBackupSize(backupS3Path);

            // OITAVO: Criar registro do backup no banco (apenas metadados)
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expirationDate = now.plusDays(30); // Backups expiram em 30 dias
            
            ThemeBackup backup = ThemeBackup.builder()
                .storeId(storeId)
                .themeId(currentThemeId)
                .themeName(currentTheme.getName())
                .backupReason(reason)
                .templateFiles(null) // ✅ Não armazenar arquivos no banco
                .templateVersion(currentTheme.getTemplateVersion())
                .templateChecksum(currentTheme.getTemplateChecksum())
                .backupS3Path(backupS3Path)
                .fileCount(backupFiles.size())
                .backupSizeBytes(actualBackupSize > 0 ? actualBackupSize : calculateBackupSize(backupFiles))
                .createdAt(now)
                .backedUpAt(now)
                .expiresAt(expirationDate)
                .isAutoBackup("THEME_CHANGE".equals(reason) || "BACKUP_RESTORE".equals(reason))
                .build();

            // Salvar backup
            try {
                backup = backupGateway.save(backup);
            } catch (Exception e) {
                throw e; // Re-throw para ser capturado pelo catch externo
            }

            // Notificar conclusão do backup
            progressService.sendBackupCompleted(storeId);

            // Limpar backups antigos do mesmo tema
            cleanupOldBackups(storeId, currentThemeId);

            return backup;

        } catch (Exception e) {
            themeErrorHandler.handleError(storeId, "create_backup", e);
            return null;
        }
    }

    /**
     * Cria backup manual
     */
    @Transactional
    public ThemeBackup createManualBackup(ULID storeId, ULID themeId) {
        return createAutomaticBackup(storeId, themeId, "MANUAL_BACKUP");
    }

    /**
     * Lista backups de uma loja
     */
    public List<ThemeBackup> listBackupsByStore(ULID storeId) {
        return backupGateway.findValidBackupsByStore(storeId, LocalDateTime.now());
    }

    /**
     * Lista backups de um tema específico
     */
    public List<ThemeBackup> listBackupsByTheme(ULID storeId, ULID themeId) {
        return backupGateway.findByStoreIdAndThemeIdOrderByCreatedAtDesc(storeId, themeId);
    }

    /**
     * Busca backup por ID
     */
    public Optional<ThemeBackup> findBackupById(ULID backupId) {
        return backupGateway.findByIdOptional(backupId);
    }

    /**
     * Busca backup mais recente de um tema
     */
    public Optional<ThemeBackup> findLatestBackup(ULID storeId, ULID themeId) {
        return backupGateway.findTopByStoreIdAndThemeIdOrderByCreatedAtDesc(storeId, themeId);
    }

    /**
     * Restaura um backup (usando a mesma lógica de ativação de tema)
     */
    @Transactional
    public boolean restoreBackup(ULID storeId, ULID backupId) {
        // Acquire lock to prevent race conditions
        if (!themeLockService.acquireThemeOperationLock(storeId)) {
            return false;
        }
        
        try {
            // 🚫 PROTEÇÃO: Marcar loja como em processo de restore para evitar race conditions com DevEnvFilter
            com.iluria.stores.layoutEditor.filter.DevEnvFilter.markStoreInRestore(storeId.toString());

            // Buscar backup
            Optional<ThemeBackup> optionalBackup = backupGateway.findByIdOptional(backupId);
            if (optionalBackup.isEmpty()) {
                return false;
            }

            ThemeBackup backup = optionalBackup.get();

            // Verificar se pertence à loja
            if (!backup.getStoreId().equals(storeId)) {
                return false;
            }

            // Verificar se backup é válido
            if (!backup.isValid()) {
                return false;
            }
            
            // Verificar se o backup existe no S3 antes de tentar restaurar
            if (!themeBackupS3Gateway.backupExists(backup.getBackupS3Path())) {
                return false;
            }

            // Buscar tema para pegar o nome
            findThemeByIdUseCase.execute(backup.getThemeId(), storeId);

            // FLUXO CORRETO: BACKUP ANTES DE QUALQUER MUDANÇA

            // 1. BAIXAR TEMPLATES DO BACKUP PRIMEIRO (para validar que existe)
            Map<String, String> templateFiles = themeBackupS3Gateway.downloadBackupFiles(backup.getBackupS3Path());
            if (templateFiles.isEmpty()) {
                return false;
            }

            // 2. CRIAR BACKUP DO TEMA ATUAL **ANTES** DE FAZER QUALQUER MUDANÇA
            Theme currentTheme = getActiveThemeUseCase.execute(storeId);
            if (currentTheme != null) {
                createAutomaticBackup(storeId, currentTheme.getId(), "BACKUP_RESTORE");
            }

            // 3. REMOVER TODOS OS ARQUIVOS HTML DO AMBIENTE DEV (não apenas do tema específico)
            int removedCount = removeAllHtmlFilesFromDev(storeId);

            // 4. RESTAURAR TEMPLATES DIRETO PARA FILE-MANAGER/DEV
            templateManagerUseCase.uploadTemplateFilesForRestore(
                backup.getThemeId(),
                backup.getStoreId(),
                templateFiles,
                backup.getTemplateVersion()
            );
            
            // 5. ARQUIVOS JÁ RESTAURADOS - não aplicar template limpo que sobrescreveria o backup

            // 6. LIMPAR CACHE LOCAL DO DevEnvFilter (após restaurar arquivos)
            forceCleanDevEnvCache(storeId);

            // 7. VALIDAÇÃO PÓS-RESTORE
            boolean validationSuccess = validateRestoreSuccess(storeId, templateFiles);
            
            if (!validationSuccess) {
                return false;
            }

            // 8. ATIVAR O TEMA (após restore bem-sucedido)
            try {
                activateThemeUseCase.execute(backup.getThemeId(), storeId);
                
                // Limpar cache novamente após ativação do tema
                forceCleanDevEnvCache(storeId);
            } catch (Exception e) {
                // Não falhar o restore por causa disso
            }

            // 9. ATUALIZAR PREVIEW DO TEMA (gerar novo preview com conteúdo restaurado)
            try {
                // Usar index.html como base para o preview (se disponível)
                String indexHtmlContent = templateFiles.get("index.html");
                if (indexHtmlContent != null && !indexHtmlContent.trim().isEmpty()) {
                    updateThemePreviewUseCase.execute(backup.getThemeId(), storeId, indexHtmlContent);
                }
            } catch (Exception e) {
                // Não falhar o restore por causa do preview
            }

            // 10. Marcar backup como restaurado
            backup.markAsRestored();
            backupGateway.save(backup);

            // ✅ PROTEÇÃO: Liberar flag de restore após conclusão
            com.iluria.stores.layoutEditor.filter.DevEnvFilter.markStoreRestoreCompleted(storeId.toString());

            // 11. Notificar via WebSocket que a restauração foi concluída
            progressService.sendThemeRestoreCompleted(storeId);

            return true;

        } catch (Exception e) {
            // ❌ PROTEÇÃO: Liberar flag de restore em caso de erro
            com.iluria.stores.layoutEditor.filter.DevEnvFilter.markStoreRestoreCompleted(storeId.toString());
            return false;
        } finally {
            // Sempre liberar o lock
            themeLockService.releaseThemeOperationLock(storeId);
        }
    }

    /**
     * Remove backup (soft delete)
     */
    @Transactional
    public boolean deleteBackup(ULID storeId, ULID backupId) {
        try {
            Optional<ThemeBackup> optionalBackup = backupGateway.findByIdOptional(backupId);
            if (optionalBackup.isEmpty()) {
                return false;
            }

            ThemeBackup backup = optionalBackup.get();
            
            // Verificar ownership
            if (!backup.getStoreId().equals(storeId)) {
                return false;
            }

            // Remover arquivos do S3
            boolean s3DeleteSuccess = themeBackupS3Gateway.deleteBackupFiles(backup.getBackupS3Path());
            if (!s3DeleteSuccess) {
                // Continuar com soft delete mesmo se S3 falhar
            }

            // Delete do banco
            backupGateway.delete(backup);

            return true;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Limpa backups antigos de um tema (mantém apenas os N mais recentes)
     */
    @Transactional
    public void cleanupOldBackups(ULID storeId, ULID themeId) {
        try {
            List<ThemeBackup> backups = backupGateway.findBackupsForCleanup(storeId, themeId);
            
            if (backups.size() > MAX_BACKUPS_PER_THEME) {
                // Manter apenas os mais recentes
                List<ThemeBackup> backupsToDeactivate = backups.subList(MAX_BACKUPS_PER_THEME, backups.size());
                
                for (ThemeBackup backup : backupsToDeactivate) {
                    backupGateway.delete(backup);
                }

            }
        } catch (Exception e) {
            // Continue without propagating error
        }
    }

    /**
     * Limpa backups expirados globalmente
     */
    @Transactional
    public void cleanupExpiredBackups() {
        try {
            List<ThemeBackup> expiredBackups = backupGateway.findExpiredBackups(LocalDateTime.now());

            for (ThemeBackup backup : expiredBackups) {
                backupGateway.delete(backup);
            }
        } catch (Exception e) {
            // Continue without propagating error
        }
    }

    /**
     * Estatísticas de backup por loja
     */
    public Map<String, Object> getBackupStats(ULID storeId) {
        long totalBackups = backupGateway.countByStoreId(storeId);
        Long totalSize = backupGateway.calculateTotalBackupSize(storeId);
        
        return Map.of(
            "totalBackups", totalBackups,
            "totalSizeBytes", totalSize != null ? totalSize : 0L,
            "totalSizeMB", totalSize != null ? totalSize / (1024.0 * 1024.0) : 0.0
        );
    }

    /**
     * Gera caminho S3 para backup
     */
    private String generateBackupS3Path(ULID storeId, ULID themeId) {
        return String.format("stores/%s/themes/%s/backups/%s",
            storeId.toString(), themeId.toString(), ULID.random().toString());
    }

    /**
     * Remove todos os arquivos HTML do ambiente DEVELOP para garantir que o backup seja restaurado limpo
     */
    private int removeAllHtmlFilesFromDev(ULID storeId) {
        try {
            // Buscar todos os arquivos HTML no ambiente DEVELOP
            List<FileManager> htmlFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeId, FileManagerEnum.DEVELOP)
                    .stream()
                    .filter(file -> file.getType() == FileManagerEnum.ARCHIVE && 
                                  file.getName() != null && 
                                  file.getName().toLowerCase().endsWith(".html"))
                    .toList();

            if (htmlFiles.isEmpty()) {
                return 0;
            }

            int removedCount = 0;
            for (FileManager file : htmlFiles) {
                try {
                    fileManagerGateway.deleteById(file.getId());
                    removedCount++;
                } catch (Exception e) {
                    // Continue with next file
                }
            }

            return removedCount;

        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * Remove todos os arquivos de um tema específico do banco de dados
     * (usado na restauração de backup para limpar arquivos antigos)
     */
    private int removeThemeFilesFromDatabase(ULID storeId, ULID themeId) {
        try {
            // Buscar todos os arquivos do tema específico no ambiente DEVELOP
            List<FileManager> themeFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeId, FileManagerEnum.DEVELOP)
                    .stream()
                    .filter(file -> file.getTemplateId() != null && file.getTemplateId().equals(themeId))
                    .toList();

            if (themeFiles.isEmpty()) {
                return 0;
            }

            int removedCount = 0;
            for (FileManager file : themeFiles) {
                try {
                    fileManagerGateway.deleteById(file.getId());
                    removedCount++;
                } catch (Exception e) {
                    // Continue with next file
                }
            }

            return removedCount;

        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * Força limpeza do cache local do DevEnvFilter para garantir que serve versão atualizada
     */
    private void forceCleanDevEnvCache(ULID storeId) {
        try {
            // Tentar limpar cache local se existir
            java.io.File devEnvDir = new java.io.File("/iluria/dev-env/" + storeId.toString());
            
            if (devEnvDir.exists() && devEnvDir.isDirectory()) {
                // Listar arquivos HTML atuais
                java.io.File[] htmlFiles = devEnvDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".html"));
                
                if (htmlFiles != null && htmlFiles.length > 0) {
                    for (java.io.File htmlFile : htmlFiles) {
                        htmlFile.delete();
                    }
                }
            }
            
            // Forçar invalidação do cache do DevEnvFilter seria ideal aqui
            // mas por enquanto dependemos da limpeza dos arquivos locais acima
            
        } catch (Exception e) {
            // Não propagar erro pois não é crítico
        }
    }

    /**
     * Valida se o restore foi bem-sucedido verificando se todos os arquivos esperados estão presentes no FileManager
     */
    private boolean validateRestoreSuccess(ULID storeId, Map<String, String> expectedFiles) {
        try {
            List<FileManager> actualFiles = fileManagerGateway.findByStoreIdAndEnvironment(storeId, FileManagerEnum.DEVELOP);
            
            int foundCount = 0;
            for (String expectedFileName : expectedFiles.keySet()) {
                boolean found = actualFiles.stream()
                    .anyMatch(file -> expectedFileName.equals(file.getName()) && file.getType() == FileManagerEnum.ARCHIVE);
                
                if (found) {
                    foundCount++;
                } else {
                    // Para index.html, tenta recriar se não encontrado
                    if ("index.html".equals(expectedFileName)) {
                        try {
                            String content = expectedFiles.get(expectedFileName);
                            if (content != null && !content.trim().isEmpty()) {
                                // Usar CreateFileUseCase injetado para recriar o arquivo
                                createFileUseCase.execute(expectedFileName, content, null, "dev", storeId);
                                foundCount++;
                            }
                        } catch (Exception retryException) {
                            // Continue without index.html
                        }
                    }
                }
            }
            
            return foundCount == expectedFiles.size();
            
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Calcula o tamanho total dos arquivos de backup
     */
    private long calculateBackupSize(Map<String, String> templateFiles) {
        return templateFiles.values().stream()
                .mapToLong(content -> content.getBytes().length)
                .sum();
    }
}